{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}لوحة تحكم الموردين{% endblock %}

{% block extra_css %}
<style>
.supplier-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.supplier-stat {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    margin-bottom: 15px;
}

.supplier-stat h3 {
    font-size: 2rem;
    margin-bottom: 5px;
    color: #fff;
}

.active-card {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.preferred-card {
    background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
}

.contracts-card {
    background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
}

.rating-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.supplier-item {
    background: white;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
}

.supplier-item.top-rated {
    border-left-color: #38ef7d;
    background: linear-gradient(135deg, rgba(56,239,125,0.1) 0%, rgba(56,239,125,0.05) 100%);
}

.contract-item {
    background: white;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #fdbb2d;
}

.contract-item.expiring {
    border-left-color: #ff6b6b;
    background: linear-gradient(135deg, rgba(255,107,107,0.1) 0%, rgba(255,107,107,0.05) 100%);
}

.chart-container {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.btn-supplier {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-supplier:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.rating-stars {
    color: #ffc107;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">🏢 لوحة تحكم الموردين</h1>
                    <p class="text-muted">إدارة شاملة للموردين والعقود والتقييمات</p>
                </div>
                <div>
                    <a href="{% url 'suppliers:supplier_list' %}" class="btn btn-supplier me-2">
                        <i class="fas fa-list"></i> جميع الموردين
                    </a>
                    <a href="{% url 'suppliers:reports' %}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-bar"></i> التقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="supplier-card active-card">
                <div class="supplier-stat">
                    <h3>{{ stats.active_suppliers }}</h3>
                    <p><i class="fas fa-check-circle"></i> موردين نشطين</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="supplier-card preferred-card">
                <div class="supplier-stat">
                    <h3>{{ stats.preferred_suppliers }}</h3>
                    <p><i class="fas fa-star"></i> موردين مفضلين</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="supplier-card contracts-card">
                <div class="supplier-stat">
                    <h3>{{ stats.active_contracts }}</h3>
                    <p><i class="fas fa-file-contract"></i> عقود نشطة</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="supplier-card rating-card">
                <div class="supplier-stat">
                    <h3>{{ stats.average_rating|floatformat:1 }}</h3>
                    <p><i class="fas fa-star"></i> متوسط التقييم</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="supplier-card">
                <h5><i class="fas fa-exclamation-triangle"></i> تنبيهات مهمة</h5>
                <div class="row">
                    <div class="col-6">
                        <div class="supplier-stat">
                            <h4>{{ stats.expiring_contracts }}</h4>
                            <p>عقود تنتهي قريباً</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="supplier-stat">
                            <h4>{{ stats.blacklisted_suppliers }}</h4>
                            <p>موردين محظورين</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="supplier-card">
                <h5><i class="fas fa-chart-pie"></i> إحصائيات عامة</h5>
                <div class="row">
                    <div class="col-6">
                        <div class="supplier-stat">
                            <h4>{{ stats.total_suppliers }}</h4>
                            <p>إجمالي الموردين</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="supplier-stat">
                            <h4>{{ stats.total_evaluations }}</h4>
                            <p>إجمالي التقييمات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Lists -->
    <div class="row">
        <!-- Supplier Types Distribution -->
        <div class="col-lg-8">
            <div class="chart-container">
                <h5><i class="fas fa-chart-pie"></i> توزيع الموردين حسب النوع</h5>
                <canvas id="supplierTypeChart" height="100"></canvas>
            </div>
        </div>

        <!-- Recent Suppliers -->
        <div class="col-lg-4">
            <div class="chart-container">
                <h5><i class="fas fa-plus-circle"></i> أحدث الموردين</h5>
                <div class="list-group list-group-flush">
                    {% for supplier in recent_suppliers %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ supplier.name_ar }}</h6>
                            <p class="mb-1 text-muted">{{ supplier.get_supplier_type_display }}</p>
                            <small>{{ supplier.created_at|date:"d/m/Y" }}</small>
                        </div>
                        <div class="text-end">
                            {% if supplier.rating %}
                            <div class="rating-stars">
                                {% for i in "12345"|make_list %}
                                    {% if forloop.counter <= supplier.rating %}
                                        <i class="fas fa-star"></i>
                                    {% else %}
                                        <i class="far fa-star"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <br>
                            <small class="badge bg-{{ supplier.is_active|yesno:'success,secondary' }}">
                                {{ supplier.is_active|yesno:'نشط,غير نشط' }}
                            </small>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>لا توجد موردين جدد</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Top Suppliers and Expiring Contracts -->
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="chart-container">
                <h5><i class="fas fa-trophy"></i> أفضل الموردين</h5>
                {% if top_suppliers %}
                <div class="row">
                    {% for supplier in top_suppliers %}
                    <div class="col-12">
                        <div class="supplier-item top-rated">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ supplier.name_ar }}</h6>
                                    <p class="mb-1 text-muted">{{ supplier.get_supplier_type_display }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-chart-line"></i> نقاط الأداء: {{ supplier.performance_score|floatformat:1 }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <div class="rating-stars mb-1">
                                        {% for i in "12345"|make_list %}
                                            {% if forloop.counter <= supplier.rating %}
                                                <i class="fas fa-star"></i>
                                            {% else %}
                                                <i class="far fa-star"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                    <small class="badge bg-success">{{ supplier.rating }}/5</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-star fa-3x mb-3"></i>
                    <h5>لا توجد تقييمات</h5>
                    <p>لم يتم تقييم أي موردين بعد</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Expiring Contracts -->
        <div class="col-lg-6">
            <div class="chart-container">
                <h5><i class="fas fa-clock"></i> العقود المنتهية قريباً</h5>
                {% if expiring_contracts %}
                <div class="row">
                    {% for contract in expiring_contracts %}
                    <div class="col-12">
                        <div class="contract-item {% if contract.days_until_expiry <= 7 %}expiring{% endif %}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ contract.title }}</h6>
                                    <p class="mb-1 text-muted">{{ contract.supplier.name_ar }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i> ينتهي في: {{ contract.end_date }}
                                        <span class="ms-2">
                                            <i class="fas fa-file-contract"></i> {{ contract.get_contract_type_display }}
                                        </span>
                                    </small>
                                </div>
                                <div class="text-end">
                                    {% if contract.days_until_expiry <= 7 %}
                                        <span class="badge bg-danger">
                                            {{ contract.days_until_expiry }} يوم
                                        </span>
                                    {% elif contract.days_until_expiry <= 30 %}
                                        <span class="badge bg-warning">
                                            {{ contract.days_until_expiry }} يوم
                                        </span>
                                    {% else %}
                                        <span class="badge bg-success">
                                            {{ contract.days_until_expiry }} يوم
                                        </span>
                                    {% endif %}
                                    <br>
                                    <small class="text-muted">{{ contract.contract_number }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-file-contract fa-3x mb-3"></i>
                    <h5>لا توجد عقود منتهية</h5>
                    <p>جميع العقود سارية لفترة طويلة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Evaluations -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="chart-container">
                <h5><i class="fas fa-star"></i> أحدث التقييمات</h5>
                {% if recent_evaluations %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>المورد</th>
                                <th>تاريخ التقييم</th>
                                <th>النقاط الإجمالية</th>
                                <th>الجودة</th>
                                <th>الخدمة</th>
                                <th>المقيم</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for evaluation in recent_evaluations %}
                            <tr>
                                <td>
                                    <strong>{{ evaluation.supplier.name_ar }}</strong>
                                    <br>
                                    <small class="text-muted">{{ evaluation.supplier.get_supplier_type_display }}</small>
                                </td>
                                <td>{{ evaluation.evaluation_date }}</td>
                                <td>
                                    <span class="badge bg-{{ evaluation.overall_score|floatformat:0|add:'0' >= 4|yesno:'success,warning' }} fs-6">
                                        {{ evaluation.overall_score|floatformat:1 }}/5
                                    </span>
                                </td>
                                <td>{{ evaluation.quality_score|floatformat:1 }}</td>
                                <td>{{ evaluation.service_score|floatformat:1 }}</td>
                                <td>{{ evaluation.evaluated_by.get_full_name }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                    <h5>لا توجد تقييمات حديثة</h5>
                    <p>لم يتم إجراء أي تقييمات مؤخراً</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Supplier Types Chart
const supplierCtx = document.getElementById('supplierTypeChart').getContext('2d');
const supplierChart = new Chart(supplierCtx, {
    type: 'doughnut',
    data: {
        labels: [{% for type in supplier_types %}'{{ type.get_supplier_type_display|default:type.supplier_type }}'{% if not forloop.last %},{% endif %}{% endfor %}],
        datasets: [{
            data: [{% for type in supplier_types %}{{ type.count }}{% if not forloop.last %},{% endif %}{% endfor %}],
            backgroundColor: [
                '#667eea',
                '#764ba2',
                '#11998e',
                '#38ef7d',
                '#fdbb2d',
                '#22c1c3',
                '#fc466b',
                '#3f5efb'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
