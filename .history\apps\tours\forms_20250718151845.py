"""
Forms for Tours app.
"""
from django import forms
from django.core.exceptions import ValidationError
# Crispy forms imports removed to fix CrispyError
# from crispy_forms.helper import FormHelper
# from crispy_forms.layout import Layout, Row, Column, Submit, HTML, Div, Field
# from crispy_forms.bootstrap import TabHolder, Tab
from .models import TourPackage, TourCategory, Destination


class TourPackageForm(forms.ModelForm):
    """Form for creating and updating tour packages."""

    class Meta:
        model = TourPackage
        fields = [
            'title_ar', 'category', 'religious_tourism_type', 'duration_days', 'duration_nights',
            'max_participants', 'min_participants', 'base_price',
            'short_description_ar', 'detailed_description_ar',
            'inclusions', 'exclusions', 'is_active'
        ]
        widgets = {
            'title_ar': forms.TextInput(attrs={'class': 'form-control'}),
            'short_description_ar': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'detailed_description_ar': forms.Textarea(attrs={'rows': 5, 'class': 'form-control'}),
            'duration_days': forms.NumberInput(attrs={'class': 'form-control', 'min': 1}),
            'duration_nights': forms.NumberInput(attrs={'class': 'form-control', 'min': 0}),
            'max_participants': forms.NumberInput(attrs={'class': 'form-control', 'min': 1}),
            'min_participants': forms.NumberInput(attrs={'class': 'form-control', 'min': 1}),
            'base_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': 0}),
            'inclusions': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'exclusions': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Make required fields
        self.fields['title_ar'].required = True
        self.fields['category'].required = True
        self.fields['duration_days'].required = True
        self.fields['base_price'].required = True

    def clean_base_price(self):
        """Validate base price."""
        price = self.cleaned_data.get('base_price')
        if price and price <= 0:
            raise ValidationError('السعر الأساسي يجب أن يكون أكبر من صفر')
        return price

    def clean_child_price(self):
        """Validate child price."""
        child_price = self.cleaned_data.get('child_price')
        base_price = self.cleaned_data.get('base_price')

        if child_price and base_price and child_price > base_price:
            raise ValidationError('سعر الطفل لا يمكن أن يكون أكبر من السعر الأساسي')

        return child_price

    def clean_infant_price(self):
        """Validate infant price."""
        infant_price = self.cleaned_data.get('infant_price')
        child_price = self.cleaned_data.get('child_price')

        if infant_price and child_price and infant_price > child_price:
            raise ValidationError('سعر الرضيع لا يمكن أن يكون أكبر من سعر الطفل')

        return infant_price

    def clean_duration_days(self):
        """Validate duration."""
        duration = self.cleaned_data.get('duration_days')
        if duration and duration <= 0:
            raise ValidationError('مدة الرحلة يجب أن تكون أكبر من صفر')
        return duration


class TourPackageSearchForm(forms.Form):
    """Form for searching tour packages."""

    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالعنوان أو الوصف...',
        })
    )

    category = forms.ModelChoiceField(
        queryset=TourCategory.objects.all(),
        required=False,
        empty_label='جميع الفئات',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    destination = forms.ModelChoiceField(
        queryset=Destination.objects.all(),
        required=False,
        empty_label='جميع الوجهات',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    is_active = forms.ChoiceField(
        choices=[('', 'الكل'), (True, 'نشطة'), (False, 'غير نشطة')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.form_class = 'form-inline'
        self.helper.layout = Layout(
            Row(
                Column('search', css_class='form-group col-md-4 mb-2'),
                Column('category', css_class='form-group col-md-2 mb-2'),
                Column('destination', css_class='form-group col-md-2 mb-2'),
                Column('is_active', css_class='form-group col-md-2 mb-2'),
                Column(
                    Submit('submit', 'بحث', css_class='btn btn-primary'),
                    css_class='form-group col-md-2 mb-2'
                ),
                css_class='form-row'
            )
        )


class TourPackageQuickAddForm(forms.ModelForm):
    """Quick form for adding basic package information."""

    class Meta:
        model = TourPackage
        fields = ['title_ar', 'category', 'destinations', 'duration_days', 'base_price']
        widgets = {
            'title_ar': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'عنوان الباقة'}),
            'duration_days': forms.NumberInput(attrs={'class': 'form-control', 'min': 1}),
            'base_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': 0}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Make all fields required
        for field in self.fields:
            self.fields[field].required = True

        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            'title_ar',
            Row(
                Column('category', css_class='form-group col-md-6 mb-3'),
                Column('destinations', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),
            Row(
                Column('duration_days', css_class='form-group col-md-6 mb-3'),
                Column('base_price', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),
            Submit('submit', 'إضافة باقة', css_class='btn btn-success btn-block mt-3')
        )
