"""
Forms for the Suppliers app.
"""
from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from .models import Supplier, SupplierContract, SupplierEvaluation


class SupplierContractForm(forms.ModelForm):
    """Form for creating and editing supplier contracts."""
    
    class Meta:
        model = SupplierContract
        fields = [
            'supplier', 'title', 'description', 'contract_type',
            'start_date', 'end_date', 'renewal_date',
            'contract_value', 'payment_terms', 'status',
            'auto_renewal', 'contract_file'
        ]
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Customize supplier field
        self.fields['supplier'].queryset = Supplier.objects.filter(is_active=True).order_by('name_ar')
        self.fields['supplier'].widget.attrs.update({
            'class': 'form-select',
            'required': True
        })
        self.fields['supplier'].label = 'المورد'
        
        # Customize title field
        self.fields['title'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'أدخل عنوان العقد',
            'required': True
        })
        self.fields['title'].label = 'عنوان العقد'
        
        # Customize description field
        self.fields['description'].widget.attrs.update({
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'وصف تفصيلي للعقد والخدمات المقدمة'
        })
        self.fields['description'].label = 'الوصف'
        
        # Customize contract_type field
        self.fields['contract_type'].widget.attrs.update({
            'class': 'form-select',
            'required': True
        })
        self.fields['contract_type'].label = 'نوع العقد'
        
        # Customize date fields
        self.fields['start_date'].widget = forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date',
            'required': True
        })
        self.fields['start_date'].label = 'تاريخ البداية'
        
        self.fields['end_date'].widget = forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date',
            'required': True
        })
        self.fields['end_date'].label = 'تاريخ النهاية'
        
        self.fields['renewal_date'].widget = forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
        self.fields['renewal_date'].label = 'تاريخ التجديد'
        
        # Customize contract_value field
        self.fields['contract_value'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': '0.00',
            'step': '0.01',
            'min': '0'
        })
        self.fields['contract_value'].label = 'قيمة العقد (درهم)'
        
        # Customize payment_terms field
        self.fields['payment_terms'].widget.attrs.update({
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'شروط الدفع والاستحقاق'
        })
        self.fields['payment_terms'].label = 'شروط الدفع'
        
        # Customize status field
        self.fields['status'].widget.attrs.update({
            'class': 'form-select'
        })
        self.fields['status'].label = 'حالة العقد'
        
        # Customize auto_renewal field
        self.fields['auto_renewal'].widget.attrs.update({
            'class': 'form-check-input'
        })
        self.fields['auto_renewal'].label = 'تجديد تلقائي'
        
        # Customize contract_file field
        self.fields['contract_file'].widget.attrs.update({
            'class': 'form-control',
            'accept': '.pdf,.doc,.docx'
        })
        self.fields['contract_file'].label = 'ملف العقد'
        
        # Set initial values
        if not self.instance.pk:
            self.fields['status'].initial = 'draft'
            self.fields['start_date'].initial = timezone.now().date()
    
    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        renewal_date = cleaned_data.get('renewal_date')
        
        # Validate date ranges
        if start_date and end_date:
            if start_date >= end_date:
                raise ValidationError({
                    'end_date': 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية'
                })
        
        if renewal_date and end_date:
            if renewal_date <= end_date:
                raise ValidationError({
                    'renewal_date': 'تاريخ التجديد يجب أن يكون بعد تاريخ النهاية'
                })
        
        # Validate contract value
        contract_value = cleaned_data.get('contract_value')
        if contract_value and contract_value < 0:
            raise ValidationError({
                'contract_value': 'قيمة العقد يجب أن تكون أكبر من أو تساوي صفر'
            })
        
        return cleaned_data
    
    def save(self, commit=True):
        contract = super().save(commit=False)
        
        # Set created_by if this is a new contract
        if not contract.pk and hasattr(self, 'user'):
            contract.created_by = self.user
        
        if commit:
            contract.save()
        
        return contract


class SupplierForm(forms.ModelForm):
    """Form for creating and editing suppliers."""
    
    class Meta:
        model = Supplier
        fields = [
            'name_ar', 'name_fr', 'name_en', 'supplier_type',
            'email', 'phone', 'website', 'address', 'city',
            'contact_person', 'contact_phone', 'contact_email',
            'payment_terms', 'services_offered', 'notes'
        ]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add CSS classes and labels
        for field_name, field in self.fields.items():
            if isinstance(field.widget, forms.Select):
                field.widget.attrs['class'] = 'form-select'
            elif isinstance(field.widget, forms.Textarea):
                field.widget.attrs['class'] = 'form-control'
                field.widget.attrs['rows'] = 3
            else:
                field.widget.attrs['class'] = 'form-control'
        
        # Set Arabic labels
        self.fields['name_ar'].label = 'الاسم بالعربية'
        self.fields['name_fr'].label = 'الاسم بالفرنسية'
        self.fields['name_en'].label = 'الاسم بالإنجليزية'
        self.fields['supplier_type'].label = 'نوع المورد'
        self.fields['email'].label = 'البريد الإلكتروني'
        self.fields['phone'].label = 'رقم الهاتف'
        self.fields['website'].label = 'الموقع الإلكتروني'
        self.fields['address'].label = 'العنوان'
        self.fields['city'].label = 'المدينة'
        self.fields['contact_person'].label = 'الشخص المسؤول'
        self.fields['contact_phone'].label = 'هاتف الشخص المسؤول'
        self.fields['contact_email'].label = 'بريد الشخص المسؤول'
        self.fields['payment_terms'].label = 'شروط الدفع'
        self.fields['services_offered'].label = 'الخدمات المقدمة'
        self.fields['notes'].label = 'ملاحظات'
        
        # Set placeholders
        self.fields['name_ar'].widget.attrs['placeholder'] = 'اسم المورد بالعربية'
        self.fields['name_fr'].widget.attrs['placeholder'] = 'Nom du fournisseur'
        self.fields['name_en'].widget.attrs['placeholder'] = 'Supplier Name'
        self.fields['email'].widget.attrs['placeholder'] = '<EMAIL>'
        self.fields['phone'].widget.attrs['placeholder'] = '+212 6XX XXX XXX'
        self.fields['website'].widget.attrs['placeholder'] = 'https://www.example.com'
        self.fields['address'].widget.attrs['placeholder'] = 'العنوان الكامل'
        self.fields['city'].widget.attrs['placeholder'] = 'الدار البيضاء'
        self.fields['contact_person'].widget.attrs['placeholder'] = 'اسم الشخص المسؤول'
        self.fields['contact_phone'].widget.attrs['placeholder'] = '+212 6XX XXX XXX'
        self.fields['contact_email'].widget.attrs['placeholder'] = '<EMAIL>'
        
        # Required fields
        self.fields['name_ar'].required = True
        self.fields['supplier_type'].required = True
        self.fields['phone'].required = True
        self.fields['contact_person'].required = True


class SupplierEvaluationForm(forms.ModelForm):
    """Form for creating and editing supplier evaluations."""
    
    class Meta:
        model = SupplierEvaluation
        fields = [
            'supplier', 'evaluation_date', 'period_start', 'period_end',
            'quality_score', 'delivery_score', 'service_score',
            'price_score', 'communication_score',
            'strengths', 'weaknesses', 'recommendations'
        ]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Customize fields
        self.fields['supplier'].queryset = Supplier.objects.filter(is_active=True).order_by('name_ar')
        self.fields['supplier'].widget.attrs.update({'class': 'form-select'})
        self.fields['supplier'].label = 'المورد'
        
        # Date fields
        for date_field in ['evaluation_date', 'period_start', 'period_end']:
            self.fields[date_field].widget = forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            })
        
        self.fields['evaluation_date'].label = 'تاريخ التقييم'
        self.fields['period_start'].label = 'بداية فترة التقييم'
        self.fields['period_end'].label = 'نهاية فترة التقييم'
        
        # Score fields
        score_fields = ['quality_score', 'delivery_score', 'service_score', 'price_score', 'communication_score']
        for field_name in score_fields:
            self.fields[field_name].widget.attrs.update({
                'class': 'form-control',
                'min': '1',
                'max': '5',
                'step': '0.1',
                'type': 'number'
            })
        
        self.fields['quality_score'].label = 'نقاط الجودة'
        self.fields['delivery_score'].label = 'نقاط التسليم'
        self.fields['service_score'].label = 'نقاط الخدمة'
        self.fields['price_score'].label = 'نقاط السعر'
        self.fields['communication_score'].label = 'نقاط التواصل'
        
        # Text fields
        for text_field in ['strengths', 'weaknesses', 'recommendations']:
            self.fields[text_field].widget.attrs.update({
                'class': 'form-control',
                'rows': 3
            })
        
        self.fields['strengths'].label = 'نقاط القوة'
        self.fields['weaknesses'].label = 'نقاط الضعف'
        self.fields['recommendations'].label = 'التوصيات'
        
        # Set initial values
        if not self.instance.pk:
            self.fields['evaluation_date'].initial = timezone.now().date()
    
    def clean(self):
        cleaned_data = super().clean()
        
        # Validate score ranges
        score_fields = ['quality_score', 'delivery_score', 'service_score', 'price_score', 'communication_score']
        for field_name in score_fields:
            score = cleaned_data.get(field_name)
            if score and (score < 1 or score > 5):
                raise ValidationError({
                    field_name: 'النقاط يجب أن تكون بين 1 و 5'
                })
        
        # Validate date ranges
        period_start = cleaned_data.get('period_start')
        period_end = cleaned_data.get('period_end')
        evaluation_date = cleaned_data.get('evaluation_date')
        
        if period_start and period_end:
            if period_start >= period_end:
                raise ValidationError({
                    'period_end': 'نهاية فترة التقييم يجب أن تكون بعد البداية'
                })
        
        if evaluation_date and period_end:
            if evaluation_date < period_end:
                raise ValidationError({
                    'evaluation_date': 'تاريخ التقييم يجب أن يكون بعد نهاية فترة التقييم'
                })
        
        return cleaned_data
