"""
Forms for HR app.
"""
from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.core.exceptions import ValidationError
from apps.accounts.models import User
from .models import Department, Position


class EmployeeCreateForm(UserCreationForm):
    """Form for creating new employees."""
    
    # Personal Information
    first_name = forms.CharField(
        max_length=150,
        label='الاسم الأول',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'أدخل الاسم الأول'
        })
    )
    
    last_name = forms.CharField(
        max_length=150,
        label='اسم العائلة',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'أدخل اسم العائلة'
        })
    )
    
    email = forms.EmailField(
        label='البريد الإلكتروني',
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        })
    )
    
    phone = forms.CharField(
        max_length=20,
        required=False,
        label='رقم الهاتف',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+212 6XX XXX XXX'
        })
    )
    
    # Employment Information
    employee_id = forms.CharField(
        max_length=20,
        required=False,
        label='رقم الموظف',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'EMP001'
        })
    )
    
    department = forms.CharField(
        max_length=100,
        required=False,
        label='القسم',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'اختر القسم'
        })
    )
    
    system_role = forms.ChoiceField(
        choices=User.SYSTEM_ROLE_CHOICES,
        label='دور النظام',
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    # Additional fields that exist in the User model
    first_name_ar = forms.CharField(
        max_length=50,
        required=False,
        label='الاسم الأول بالعربية',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'الاسم الأول بالعربية'
        })
    )

    last_name_ar = forms.CharField(
        max_length=50,
        required=False,
        label='اسم العائلة بالعربية',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'اسم العائلة بالعربية'
        })
    )

    hire_date = forms.DateField(
        required=False,
        label='تاريخ التوظيف',
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    class Meta:
        model = User
        fields = [
            'username', 'password1', 'password2',
            'first_name', 'last_name', 'email', 'phone',
            'employee_id', 'department', 'system_role',
            'first_name_ar', 'last_name_ar', 'hire_date'
        ]
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Customize username field
        self.fields['username'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'اسم المستخدم'
        })
        self.fields['username'].label = 'اسم المستخدم'
        
        # Customize password fields
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'كلمة المرور'
        })
        self.fields['password1'].label = 'كلمة المرور'
        
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'تأكيد كلمة المرور'
        })
        self.fields['password2'].label = 'تأكيد كلمة المرور'
        
        # Populate department choices from existing departments
        departments = Department.objects.filter(is_active=True)
        if departments.exists():
            department_choices = [(dept.name_ar, dept.name_ar) for dept in departments]
            self.fields['department'] = forms.ChoiceField(
                choices=[('', 'اختر القسم')] + department_choices,
                required=False,
                label='القسم',
                widget=forms.Select(attrs={'class': 'form-select'})
            )
    
    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email and User.objects.filter(email=email).exists():
            raise ValidationError('هذا البريد الإلكتروني مستخدم بالفعل.')
        return email
    
    def clean_employee_id(self):
        employee_id = self.cleaned_data.get('employee_id')
        if employee_id and User.objects.filter(employee_id=employee_id).exists():
            raise ValidationError('رقم الموظف هذا مستخدم بالفعل.')
        return employee_id
    
    def save(self, commit=True):
        user = super().save(commit=False)
        user.is_active = True
        if commit:
            user.save()
        return user


class EmployeeUpdateForm(forms.ModelForm):
    """Form for updating employee information."""

    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'email', 'phone',
            'employee_id', 'department', 'system_role',
            'first_name_ar', 'last_name_ar', 'hire_date',
            'is_active'
        ]
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add CSS classes to all fields
        for field_name, field in self.fields.items():
            if isinstance(field.widget, forms.Select):
                field.widget.attrs['class'] = 'form-select'
            elif isinstance(field.widget, forms.Textarea):
                field.widget.attrs['class'] = 'form-control'
                field.widget.attrs['rows'] = 3
            elif isinstance(field.widget, forms.CheckboxInput):
                field.widget.attrs['class'] = 'form-check-input'
            else:
                field.widget.attrs['class'] = 'form-control'
        
        # Set labels in Arabic
        self.fields['first_name'].label = 'الاسم الأول'
        self.fields['last_name'].label = 'اسم العائلة'
        self.fields['email'].label = 'البريد الإلكتروني'
        self.fields['phone'].label = 'رقم الهاتف'
        self.fields['employee_id'].label = 'رقم الموظف'
        self.fields['department'].label = 'القسم'
        self.fields['system_role'].label = 'دور النظام'
        self.fields['first_name_ar'].label = 'الاسم الأول بالعربية'
        self.fields['last_name_ar'].label = 'اسم العائلة بالعربية'
        self.fields['hire_date'].label = 'تاريخ التوظيف'
        self.fields['is_active'].label = 'نشط'
        
        # Set date widget
        self.fields['hire_date'].widget = forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
        
        # Populate department choices
        departments = Department.objects.filter(is_active=True)
        if departments.exists():
            department_choices = [(dept.name_ar, dept.name_ar) for dept in departments]
            self.fields['department'] = forms.ChoiceField(
                choices=[('', 'اختر القسم')] + department_choices,
                required=False,
                label='القسم',
                widget=forms.Select(attrs={'class': 'form-select'})
            )
