"""
URL configuration for tours app.
"""
from django.urls import path
from . import views

app_name = 'tours'

urlpatterns = [
    # Package URLs
    path('', views.PackageListView.as_view(), name='package_list'),
    path('packages/', views.PackageListView.as_view(), name='package_list'),
    path('packages/add/', views.PackageCreateView.as_view(), name='package_add'),
    path('packages/<int:pk>/', views.PackageDetailView.as_view(), name='package_detail'),
    path('packages/<int:pk>/edit/', views.PackageUpdateView.as_view(), name='package_edit'),
    path('packages/<int:pk>/delete/', views.PackageDeleteView.as_view(), name='package_delete'),

    # Destination URLs
    path('destinations/', views.DestinationListView.as_view(), name='destination_list'),
    path('destinations/add/', views.DestinationCreateView.as_view(), name='destination_add'),
    path('destinations/<int:pk>/', views.DestinationDetailView.as_view(), name='destination_detail'),
    path('destinations/<int:pk>/edit/', views.DestinationUpdateView.as_view(), name='destination_edit'),
    path('destinations/<int:pk>/delete/', views.DestinationDeleteView.as_view(), name='destination_delete'),

    # Package-Destination Management
    path('packages/<int:package_id>/destinations/', views.package_destinations_manage, name='package_destinations_manage'),

    # Itinerary URLs
    path('packages/<int:package_id>/itinerary/', views.ItineraryListView.as_view(), name='itinerary_list'),
    path('packages/<int:package_id>/itinerary/add/', views.ItineraryCreateView.as_view(), name='itinerary_add'),
    path('itinerary/<int:pk>/edit/', views.ItineraryUpdateView.as_view(), name='itinerary_edit'),

    # Availability URLs
    path('packages/<int:package_id>/availability/', views.AvailabilityListView.as_view(), name='availability_list'),
    path('packages/<int:package_id>/availability/add/', views.AvailabilityCreateView.as_view(), name='availability_add'),
    path('availability/<int:pk>/edit/', views.AvailabilityUpdateView.as_view(), name='availability_edit'),
]
