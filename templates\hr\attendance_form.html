{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
.form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    text-align: center;
}

.form-section {
    margin-bottom: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.form-section h5 {
    color: #495057;
    margin-bottom: 20px;
    font-weight: 600;
}

.form-section h5 i {
    margin-left: 10px;
    color: #667eea;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.required-field::after {
    content: " *";
    color: #dc3545;
}

.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-submit {
    background: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(56, 239, 125, 0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
    margin-left: 15px;
}

.btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
    color: white;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
    display: block;
}

.help-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 5px;
}

.form-tips {
    background: linear-gradient(135deg, rgba(56, 239, 125, 0.1) 0%, rgba(17, 153, 142, 0.1) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    border-left: 4px solid #38ef7d;
}

.form-tips h6 {
    color: #11998e;
    margin-bottom: 15px;
    font-weight: 600;
}

.form-tips ul {
    margin-bottom: 0;
    padding-right: 20px;
}

.form-tips li {
    color: #495057;
    margin-bottom: 5px;
}

.attendance-preview {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    border-left: 4px solid #667eea;
}

.attendance-preview h6 {
    color: #667eea;
    margin-bottom: 15px;
    font-weight: 600;
}

.preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(102, 126, 234, 0.2);
}

.preview-item:last-child {
    border-bottom: none;
}

.preview-label {
    color: #6c757d;
    font-weight: 500;
}

.preview-value {
    color: #495057;
    font-weight: 600;
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-left: 8px;
}

.status-present { background-color: #38ef7d; }
.status-absent { background-color: #ff6b6b; }
.status-late { background-color: #fdbb2d; }
.status-half_day { background-color: #667eea; }
.status-on_leave { background-color: #6c757d; }

.time-calculator {
    background: linear-gradient(135deg, rgba(253, 187, 45, 0.1) 0%, rgba(243, 156, 18, 0.1) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    border-left: 4px solid #fdbb2d;
}

.time-calculator h6 {
    color: #f39c12;
    margin-bottom: 15px;
    font-weight: 600;
}

.calculated-hours {
    font-size: 2rem;
    font-weight: bold;
    color: #f39c12;
    text-align: center;
    margin: 15px 0;
}

@media (max-width: 768px) {
    .form-container {
        padding: 20px;
    }
    
    .form-section {
        padding: 15px;
    }
    
    .btn-submit, .btn-cancel {
        width: 100%;
        margin-bottom: 10px;
        margin-left: 0;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="form-header">
        <h1 class="h3 mb-3">⏰ {{ title }}</h1>
        <p class="mb-0">تسجيل ومتابعة حضور الموظفين</p>
    </div>

    <!-- Form Tips -->
    <div class="form-tips">
        <h6><i class="fas fa-lightbulb"></i> نصائح مهمة</h6>
        <ul>
            <li>تأكد من صحة التاريخ والأوقات قبل التسجيل</li>
            <li>وقت الدخول مطلوب للحالة "حاضر"</li>
            <li>سيتم احتساب الساعات الفعلية تلقائياً</li>
            <li>لا يمكن تسجيل حضور في تاريخ مستقبلي</li>
            <li>لا يمكن تسجيل حضور مكرر لنفس الموظف في نفس التاريخ</li>
        </ul>
    </div>

    <form method="post" id="attendanceForm">
        {% csrf_token %}
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="form-section">
                    <h5><i class="fas fa-user"></i>معلومات الموظف</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.employee.label }}</label>
                                {{ form.employee }}
                                {% if form.employee.errors %}
                                    <span class="error-message">{{ form.employee.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.date.label }}</label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <span class="error-message">{{ form.date.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Time Information -->
                <div class="form-section">
                    <h5><i class="fas fa-clock"></i>أوقات الحضور</h5>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">{{ form.check_in_time.label }}</label>
                                {{ form.check_in_time }}
                                {% if form.check_in_time.errors %}
                                    <span class="error-message">{{ form.check_in_time.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">{{ form.check_out_time.label }}</label>
                                {{ form.check_out_time }}
                                {% if form.check_out_time.errors %}
                                    <span class="error-message">{{ form.check_out_time.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">{{ form.scheduled_hours.label }}</label>
                                {{ form.scheduled_hours }}
                                {% if form.scheduled_hours.errors %}
                                    <span class="error-message">{{ form.scheduled_hours.errors.0 }}</span>
                                {% endif %}
                                <div class="help-text">الساعات المجدولة للعمل</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">{{ form.overtime_hours.label }}</label>
                                {{ form.overtime_hours }}
                                {% if form.overtime_hours.errors %}
                                    <span class="error-message">{{ form.overtime_hours.errors.0 }}</span>
                                {% endif %}
                                <div class="help-text">الساعات الإضافية</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status and Notes -->
                <div class="form-section">
                    <h5><i class="fas fa-info-circle"></i>الحالة والملاحظات</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.status.label }}</label>
                                {{ form.status }}
                                {% if form.status.errors %}
                                    <span class="error-message">{{ form.status.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <span class="error-message">{{ form.notes.errors.0 }}</span>
                                {% endif %}
                                <div class="help-text">ملاحظات إضافية حول الحضور</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="text-center">
                    <button type="submit" class="btn btn-submit">
                        <i class="fas fa-save"></i> {{ submit_text|default:"تسجيل الحضور" }}
                    </button>
                    <a href="{% url 'hr:attendance_list' %}" class="btn btn-cancel">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Attendance Preview -->
                <div class="attendance-preview">
                    <h6><i class="fas fa-eye"></i> معاينة الحضور</h6>
                    <div id="attendancePreview">
                        <div class="preview-item">
                            <span class="preview-label">الموظف:</span>
                            <span class="preview-value" id="previewEmployee">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">التاريخ:</span>
                            <span class="preview-value" id="previewDate">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">الحالة:</span>
                            <span class="preview-value" id="previewStatus">
                                <span class="status-indicator status-present"></span>
                                غير محدد
                            </span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">وقت الدخول:</span>
                            <span class="preview-value" id="previewCheckIn">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">وقت الخروج:</span>
                            <span class="preview-value" id="previewCheckOut">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">الساعات المجدولة:</span>
                            <span class="preview-value" id="previewScheduled">8.0</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">الساعات الإضافية:</span>
                            <span class="preview-value" id="previewOvertime">0.0</span>
                        </div>
                    </div>
                </div>

                <!-- Time Calculator -->
                <div class="time-calculator">
                    <h6><i class="fas fa-calculator"></i> حاسبة الساعات</h6>
                    <div class="text-center">
                        <div>الساعات الفعلية المحسوبة:</div>
                        <div class="calculated-hours" id="calculatedHours">0.0</div>
                        <small class="text-muted">ساعة</small>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form validation and preview
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('attendanceForm');
    const employeeField = document.getElementById('{{ form.employee.id_for_label }}');
    const dateField = document.getElementById('{{ form.date.id_for_label }}');
    const statusField = document.getElementById('{{ form.status.id_for_label }}');
    const checkInField = document.getElementById('{{ form.check_in_time.id_for_label }}');
    const checkOutField = document.getElementById('{{ form.check_out_time.id_for_label }}');
    const scheduledField = document.getElementById('{{ form.scheduled_hours.id_for_label }}');
    const overtimeField = document.getElementById('{{ form.overtime_hours.id_for_label }}');
    
    // Status colors mapping
    const statusColors = {
        'present': 'status-present',
        'absent': 'status-absent',
        'late': 'status-late',
        'half_day': 'status-half_day',
        'on_leave': 'status-on_leave'
    };
    
    // Update preview
    function updatePreview() {
        document.getElementById('previewEmployee').textContent = 
            employeeField.options[employeeField.selectedIndex]?.text || 'غير محدد';
        document.getElementById('previewDate').textContent = 
            dateField.value || 'غير محدد';
        
        const statusText = statusField.options[statusField.selectedIndex]?.text || 'غير محدد';
        const statusValue = statusField.value;
        const statusIndicator = document.querySelector('#previewStatus .status-indicator');
        
        // Update status with color indicator
        statusIndicator.className = 'status-indicator ' + (statusColors[statusValue] || 'status-present');
        document.getElementById('previewStatus').innerHTML = 
            '<span class="status-indicator ' + (statusColors[statusValue] || 'status-present') + '"></span>' + statusText;
        
        document.getElementById('previewCheckIn').textContent = 
            checkInField.value || 'غير محدد';
        document.getElementById('previewCheckOut').textContent = 
            checkOutField.value || 'غير محدد';
        document.getElementById('previewScheduled').textContent = 
            scheduledField.value || '8.0';
        document.getElementById('previewOvertime').textContent = 
            overtimeField.value || '0.0';
    }
    
    // Calculate actual hours
    function calculateHours() {
        if (checkInField.value && checkOutField.value) {
            const checkIn = new Date('2000-01-01 ' + checkInField.value);
            let checkOut = new Date('2000-01-01 ' + checkOutField.value);
            
            // Handle overnight shifts
            if (checkOut < checkIn) {
                checkOut.setDate(checkOut.getDate() + 1);
            }
            
            const timeDiff = checkOut - checkIn;
            const hours = timeDiff / (1000 * 60 * 60);
            
            document.getElementById('calculatedHours').textContent = hours.toFixed(1);
        } else {
            document.getElementById('calculatedHours').textContent = '0.0';
        }
    }
    
    // Add event listeners
    [employeeField, dateField, statusField, checkInField, checkOutField, scheduledField, overtimeField].forEach(field => {
        if (field) {
            field.addEventListener('change', function() {
                updatePreview();
                calculateHours();
            });
            field.addEventListener('input', function() {
                updatePreview();
                calculateHours();
            });
        }
    });
    
    // Initial preview update
    updatePreview();
    calculateHours();
    
    // Form validation
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        const requiredFields = [employeeField, dateField, statusField];
        requiredFields.forEach(field => {
            if (field && !field.value.trim()) {
                isValid = false;
                field.style.borderColor = '#dc3545';
            } else if (field) {
                field.style.borderColor = '#e9ecef';
            }
        });
        
        // Check if present status requires check-in time
        if (statusField.value === 'present' && !checkInField.value) {
            isValid = false;
            checkInField.style.borderColor = '#dc3545';
            alert('وقت الدخول مطلوب للحالة "حاضر"');
        }
        
        // Check time validation
        if (checkInField.value && checkOutField.value) {
            const checkIn = new Date('2000-01-01 ' + checkInField.value);
            let checkOut = new Date('2000-01-01 ' + checkOutField.value);
            
            if (checkOut <= checkIn && checkOut.getHours() > checkIn.getHours()) {
                // This is likely an overnight shift, which is valid
            } else if (checkOut <= checkIn) {
                isValid = false;
                checkOutField.style.borderColor = '#dc3545';
                alert('وقت الخروج يجب أن يكون بعد وقت الدخول');
            }
        }
        
        // Check if date is in the future
        if (dateField.value) {
            const selectedDate = new Date(dateField.value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (selectedDate > today) {
                isValid = false;
                dateField.style.borderColor = '#dc3545';
                alert('لا يمكن تسجيل حضور في تاريخ مستقبلي');
            }
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
