"""
HR views for the Moroccan Travel Agency ERP system.
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Q, Avg
from django.utils import timezone
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from datetime import timedelta
from .models import Department, Position, Leave, Attendance, Payroll
from .forms import (
    EmployeeCreateForm, EmployeeUpdateForm, LeaveForm, LeaveApprovalForm,
    AttendanceForm, BulkAttendanceForm
)
from apps.accounts.models import User


@login_required
def hr_dashboard(request):
    """HR dashboard view."""
    today = timezone.now().date()
    current_month = today.replace(day=1)

    # Employee statistics
    stats = {
        'total_employees': User.objects.filter(is_active=True).count(),
        'total_departments': Department.objects.filter(is_active=True).count(),
        'total_positions': Position.objects.filter(is_active=True).count(),
        'pending_leaves': Leave.objects.filter(status='pending').count(),
        'employees_on_leave': Leave.objects.filter(
            status='approved',
            start_date__lte=today,
            end_date__gte=today
        ).count(),
        'monthly_attendance_rate': 0,  # Will calculate below
        'total_payroll': Payroll.objects.filter(
            pay_period_end__gte=current_month,
            status='paid'
        ).aggregate(total=Sum('net_salary'))['total'] or 0,
    }

    # Calculate attendance rate
    total_working_days = Attendance.objects.filter(
        date__gte=current_month
    ).count()
    present_days = Attendance.objects.filter(
        date__gte=current_month,
        status='present'
    ).count()

    if total_working_days > 0:
        stats['monthly_attendance_rate'] = round((present_days / total_working_days) * 100, 1)

    # Recent leaves
    recent_leaves = Leave.objects.select_related('employee').order_by('-created_at')[:5]

    # Upcoming leaves
    upcoming_leaves = Leave.objects.filter(
        status='approved',
        start_date__gte=today,
        start_date__lte=today + timedelta(days=14)
    ).select_related('employee').order_by('start_date')[:10]

    # Department distribution
    department_stats = []
    for dept in Department.objects.filter(is_active=True):
        employee_count = User.objects.filter(
            is_active=True,
            department=dept.name_ar
        ).count()
        dept.employee_count = employee_count
        department_stats.append(dept)

    # Sort by employee count
    department_stats.sort(key=lambda x: x.employee_count, reverse=True)

    # Attendance trends
    attendance_data = []
    for i in range(7):
        date = today - timedelta(days=i)
        total = Attendance.objects.filter(date=date).count()
        present = Attendance.objects.filter(date=date, status='present').count()
        rate = round((present / total * 100) if total > 0 else 0, 1)
        attendance_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'rate': rate
        })

    context = {
        'stats': stats,
        'recent_leaves': recent_leaves,
        'upcoming_leaves': upcoming_leaves,
        'department_stats': department_stats,
        'attendance_data': list(reversed(attendance_data)),
    }

    return render(request, 'hr/dashboard.html', context)


class EmployeeListView(LoginRequiredMixin, ListView):
    """List view for employees."""
    model = User
    template_name = 'hr/employee_list.html'
    context_object_name = 'employees'
    paginate_by = 20

    def get_queryset(self):
        queryset = User.objects.filter(is_active=True).order_by('last_name', 'first_name')

        # Filter by department (CharField)
        department = self.request.GET.get('department')
        if department:
            queryset = queryset.filter(department=department)

        # Filter by system role
        role = self.request.GET.get('role')
        if role:
            queryset = queryset.filter(system_role=role)

        # Search
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(email__icontains=search) |
                Q(employee_id__icontains=search)
            )

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['departments'] = Department.objects.filter(is_active=True)
        context['system_roles'] = User.SYSTEM_ROLE_CHOICES
        return context


class LeaveListView(LoginRequiredMixin, ListView):
    """List view for leaves."""
    model = Leave
    template_name = 'hr/leave_list.html'
    context_object_name = 'leaves'
    paginate_by = 20

    def get_queryset(self):
        queryset = Leave.objects.select_related('employee').order_by('-start_date')

        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by leave type
        leave_type = self.request.GET.get('leave_type')
        if leave_type:
            queryset = queryset.filter(leave_type=leave_type)

        # Filter by employee
        employee = self.request.GET.get('employee')
        if employee:
            queryset = queryset.filter(employee_id=employee)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Leave.STATUS_CHOICES
        context['leave_type_choices'] = Leave.LEAVE_TYPE_CHOICES
        context['employees'] = User.objects.filter(is_active=True)
        return context


class AttendanceListView(LoginRequiredMixin, ListView):
    """List view for attendance."""
    model = Attendance
    template_name = 'hr/attendance_list.html'
    context_object_name = 'attendances'
    paginate_by = 20

    def get_queryset(self):
        queryset = Attendance.objects.select_related('employee').order_by('-date', 'employee')

        # Filter by date range
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        if date_from:
            queryset = queryset.filter(date__gte=date_from)
        if date_to:
            queryset = queryset.filter(date__lte=date_to)

        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by employee
        employee = self.request.GET.get('employee')
        if employee:
            queryset = queryset.filter(employee_id=employee)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Attendance.STATUS_CHOICES
        context['employees'] = User.objects.filter(is_active=True)
        return context


class AttendanceCreateView(LoginRequiredMixin, CreateView):
    """Create view for attendance records."""
    model = Attendance
    form_class = AttendanceForm
    template_name = 'hr/attendance_form.html'
    success_url = reverse_lazy('hr:attendance_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        messages.success(
            self.request,
            'تم تسجيل الحضور بنجاح'
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'تسجيل حضور جديد'
        context['submit_text'] = 'تسجيل الحضور'
        return context


class AttendanceUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for attendance records."""
    model = Attendance
    form_class = AttendanceForm
    template_name = 'hr/attendance_form.html'
    success_url = reverse_lazy('hr:attendance_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        messages.success(
            self.request,
            'تم تحديث سجل الحضور بنجاح'
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = f'تعديل حضور: {self.object.employee.get_full_name()}'
        context['submit_text'] = 'حفظ التغييرات'
        return context


class AttendanceDetailView(LoginRequiredMixin, DetailView):
    """Detail view for attendance records."""
    model = Attendance
    template_name = 'hr/attendance_detail.html'
    context_object_name = 'attendance'


class BulkAttendanceView(LoginRequiredMixin, CreateView):
    """Bulk attendance entry view."""
    form_class = BulkAttendanceForm
    template_name = 'hr/bulk_attendance.html'
    success_url = reverse_lazy('hr:attendance_list')

    def form_valid(self, form):
        date = form.cleaned_data['date']
        employees = form.cleaned_data['employees']
        status = form.cleaned_data['status']
        check_in_time = form.cleaned_data.get('check_in_time')
        check_out_time = form.cleaned_data.get('check_out_time')
        scheduled_hours = form.cleaned_data['scheduled_hours']
        notes = form.cleaned_data.get('notes', '')

        created_count = 0
        for employee in employees:
            attendance = Attendance.objects.create(
                employee=employee,
                date=date,
                status=status,
                check_in_time=check_in_time,
                check_out_time=check_out_time,
                scheduled_hours=scheduled_hours,
                notes=notes,
                recorded_by=self.request.user
            )

            # Calculate actual hours if both times are provided
            if check_in_time and check_out_time:
                from datetime import datetime, timedelta

                check_in = datetime.combine(date, check_in_time)
                check_out = datetime.combine(date, check_out_time)

                if check_out < check_in:
                    check_out += timedelta(days=1)

                time_diff = check_out - check_in
                attendance.actual_hours = round(time_diff.total_seconds() / 3600, 2)
                attendance.save()

            created_count += 1

        messages.success(
            self.request,
            f'تم تسجيل الحضور لـ {created_count} موظف بنجاح'
        )
        return redirect(self.success_url)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'تسجيل حضور جماعي'
        context['submit_text'] = 'تسجيل الحضور للجميع'
        return context


class PayrollListView(LoginRequiredMixin, ListView):
    """List view for payroll."""
    model = Payroll
    template_name = 'hr/payroll_list.html'
    context_object_name = 'payrolls'
    paginate_by = 20

    def get_queryset(self):
        queryset = Payroll.objects.select_related('employee').order_by('-pay_period_end', 'employee')

        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by pay period
        period = self.request.GET.get('period')
        if period:
            try:
                year, month = period.split('-')
                queryset = queryset.filter(
                    pay_period_end__year=int(year),
                    pay_period_end__month=int(month)
                )
            except ValueError:
                pass

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Payroll.STATUS_CHOICES
        return context


class LeaveCreateView(LoginRequiredMixin, CreateView):
    """Create view for leave requests."""
    model = Leave
    form_class = LeaveForm
    template_name = 'hr/leave_form.html'
    success_url = reverse_lazy('hr:leave_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        form.instance.requested_by = self.request.user
        messages.success(
            self.request,
            f'تم إرسال طلب الإجازة بنجاح'
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'طلب إجازة جديد'
        context['submit_text'] = 'إرسال الطلب'
        return context


class LeaveUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for leave requests."""
    model = Leave
    form_class = LeaveForm
    template_name = 'hr/leave_form.html'
    success_url = reverse_lazy('hr:leave_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        messages.success(
            self.request,
            f'تم تحديث طلب الإجازة بنجاح'
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = f'تعديل طلب الإجازة'
        context['submit_text'] = 'حفظ التغييرات'
        return context


class LeaveDetailView(LoginRequiredMixin, DetailView):
    """Detail view for leave requests."""
    model = Leave
    template_name = 'hr/leave_detail.html'
    context_object_name = 'leave'


class LeaveApprovalView(LoginRequiredMixin, UpdateView):
    """View for approving/rejecting leave requests."""
    model = Leave
    form_class = LeaveApprovalForm
    template_name = 'hr/leave_approval.html'
    success_url = reverse_lazy('hr:leave_list')

    def form_valid(self, form):
        form.instance.approved_by = self.request.user
        status = form.cleaned_data['status']
        status_text = 'موافق عليها' if status == 'approved' else 'مرفوضة'
        messages.success(
            self.request,
            f'تم تحديث حالة الإجازة إلى: {status_text}'
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = f'مراجعة طلب الإجازة'
        return context


@login_required
def hr_reports(request):
    """HR reports view."""
    today = timezone.now().date()
    current_month = today.replace(day=1)

    # Employee statistics
    employee_stats = {
        'total_employees': User.objects.filter(is_active=True).count(),
        'new_employees': User.objects.filter(
            date_joined__gte=current_month,
            is_active=True
        ).count(),
        'employees_by_department': [],  # Will be populated below
    }

    # Populate employees by department
    employees_by_department = []
    for dept in Department.objects.filter(is_active=True):
        employee_count = User.objects.filter(
            is_active=True,
            department=dept.name_ar
        ).count()
        dept.employee_count = employee_count
        employees_by_department.append(dept)

    # Sort by employee count
    employees_by_department.sort(key=lambda x: x.employee_count, reverse=True)
    employee_stats['employees_by_department'] = employees_by_department

    # Leave statistics
    leave_stats = {
        'total_leaves': Leave.objects.count(),
        'pending_leaves': Leave.objects.filter(status='pending').count(),
        'approved_leaves': Leave.objects.filter(status='approved').count(),
        'monthly_leaves': Leave.objects.filter(
            start_date__gte=current_month
        ).count(),
        'leave_by_type': Leave.objects.values('leave_type').annotate(
            count=Count('id')
        ).order_by('-count'),
    }

    # Attendance statistics
    attendance_stats = {
        'total_attendance_records': Attendance.objects.count(),
        'monthly_attendance_rate': 0,
        'average_working_hours': Attendance.objects.filter(
            date__gte=current_month
        ).aggregate(avg=Avg('actual_hours'))['avg'] or 0,
        'total_overtime_hours': Attendance.objects.filter(
            date__gte=current_month
        ).aggregate(total=Sum('overtime_hours'))['total'] or 0,
    }

    # Calculate monthly attendance rate
    total_working_days = Attendance.objects.filter(date__gte=current_month).count()
    present_days = Attendance.objects.filter(
        date__gte=current_month,
        status='present'
    ).count()

    if total_working_days > 0:
        attendance_stats['monthly_attendance_rate'] = round(
            (present_days / total_working_days) * 100, 1
        )

    # Payroll statistics
    payroll_stats = {
        'total_payroll': Payroll.objects.filter(
            pay_period_end__gte=current_month,
            status='paid'
        ).aggregate(total=Sum('net_salary'))['total'] or 0,
        'average_salary': Payroll.objects.filter(
            pay_period_end__gte=current_month,
            status='paid'
        ).aggregate(avg=Avg('net_salary'))['avg'] or 0,
        'total_overtime_pay': Payroll.objects.filter(
            pay_period_end__gte=current_month,
            status='paid'
        ).aggregate(total=Sum('overtime_amount'))['total'] or 0,
    }

    context = {
        'employee_stats': employee_stats,
        'leave_stats': leave_stats,
        'attendance_stats': attendance_stats,
        'payroll_stats': payroll_stats,
    }

    return render(request, 'hr/reports.html', context)


class EmployeeCreateView(LoginRequiredMixin, CreateView):
    """Create view for employees."""
    model = User
    form_class = EmployeeCreateForm
    template_name = 'hr/employee_form.html'
    success_url = reverse_lazy('hr:employee_list')

    def form_valid(self, form):
        messages.success(
            self.request,
            f'تم إنشاء الموظف {form.instance.get_full_name()} بنجاح'
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'إضافة موظف جديد'
        context['submit_text'] = 'إضافة الموظف'
        return context


class EmployeeDetailView(LoginRequiredMixin, DetailView):
    """Detail view for employees."""
    model = User
    template_name = 'hr/employee_detail.html'
    context_object_name = 'employee'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        employee = self.get_object()

        # Get employee's recent leaves
        context['recent_leaves'] = Leave.objects.filter(
            employee=employee
        ).order_by('-created_at')[:5]

        # Get employee's recent attendance
        context['recent_attendance'] = Attendance.objects.filter(
            employee=employee
        ).order_by('-date')[:10]

        # Get employee's recent payroll
        context['recent_payroll'] = Payroll.objects.filter(
            employee=employee
        ).order_by('-pay_date')[:5]

        return context


class EmployeeUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for employees."""
    model = User
    form_class = EmployeeUpdateForm
    template_name = 'hr/employee_form.html'

    def get_success_url(self):
        return reverse_lazy('hr:employee_detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        messages.success(
            self.request,
            f'تم تحديث بيانات الموظف {form.instance.get_full_name()} بنجاح'
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = f'تعديل بيانات {self.object.get_full_name()}'
        context['submit_text'] = 'حفظ التغييرات'
        return context
