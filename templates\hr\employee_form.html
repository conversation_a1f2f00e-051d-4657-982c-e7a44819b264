{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
.form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    text-align: center;
}

.form-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
    border-left: 4px solid #667eea;
}

.form-section h5 {
    color: #495057;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.form-section h5 i {
    margin-left: 10px;
    color: #667eea;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

.form-control,
.form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-submit {
    background: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 150px;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(56, 239, 125, 0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 150px;
    margin-left: 15px;
}

.btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
    color: white;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
}

.form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 5px;
}

.required-field::after {
    content: " *";
    color: #dc3545;
}

.password-strength {
    margin-top: 10px;
}

.strength-bar {
    height: 4px;
    border-radius: 2px;
    background: #e9ecef;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    transition: all 0.3s ease;
}

.strength-weak { background: #dc3545; width: 25%; }
.strength-fair { background: #ffc107; width: 50%; }
.strength-good { background: #28a745; width: 75%; }
.strength-strong { background: #20c997; width: 100%; }

.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
}

.alert-danger {
    background: #f8d7da;
    color: #721c24;
}

.form-actions {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    text-align: center;
    margin-top: 30px;
}

@media (max-width: 768px) {
    .form-container {
        padding: 20px;
    }
    
    .form-section {
        padding: 15px;
    }
    
    .btn-submit,
    .btn-cancel {
        width: 100%;
        margin: 5px 0;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="form-header">
        <h1 class="h3 mb-3">👤 {{ title }}</h1>
        <p class="mb-0">املأ جميع الحقول المطلوبة لإضافة موظف جديد</p>
    </div>

    <form method="post" class="needs-validation" novalidate>
        {% csrf_token %}
        
        <!-- Display form errors -->
        {% if form.non_field_errors %}
        <div class="alert alert-danger">
            {{ form.non_field_errors }}
        </div>
        {% endif %}

        <div class="row">
            <div class="col-lg-8">
                <!-- Login Information -->
                <div class="form-section">
                    <h5><i class="fas fa-key"></i>معلومات تسجيل الدخول</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.username.label }}</label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="error-message">{{ form.username.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">سيستخدم هذا الاسم لتسجيل الدخول</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.email.label }}</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="error-message">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.password1.label }}</label>
                                {{ form.password1 }}
                                {% if form.password1.errors %}
                                    <div class="error-message">{{ form.password1.errors.0 }}</div>
                                {% endif %}
                                <div class="password-strength">
                                    <div class="strength-bar">
                                        <div class="strength-fill" id="strengthBar"></div>
                                    </div>
                                    <small id="strengthText" class="form-text"></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.password2.label }}</label>
                                {{ form.password2 }}
                                {% if form.password2.errors %}
                                    <div class="error-message">{{ form.password2.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Personal Information -->
                <div class="form-section">
                    <h5><i class="fas fa-user"></i>المعلومات الشخصية</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.first_name.label }}</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="error-message">{{ form.first_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.last_name.label }}</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="error-message">{{ form.last_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ form.phone.label }}</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="error-message">{{ form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ form.first_name_ar.label }}</label>
                                {{ form.first_name_ar }}
                                {% if form.first_name_ar.errors %}
                                    <div class="error-message">{{ form.first_name_ar.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ form.last_name_ar.label }}</label>
                                {{ form.last_name_ar }}
                                {% if form.last_name_ar.errors %}
                                    <div class="error-message">{{ form.last_name_ar.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ form.hire_date.label }}</label>
                                {{ form.hire_date }}
                                {% if form.hire_date.errors %}
                                    <div class="error-message">{{ form.hire_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Employment Information -->
                <div class="form-section">
                    <h5><i class="fas fa-briefcase"></i>معلومات العمل</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ form.employee_id.label }}</label>
                                {{ form.employee_id }}
                                {% if form.employee_id.errors %}
                                    <div class="error-message">{{ form.employee_id.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">سيتم إنشاؤه تلقائياً إذا ترك فارغاً</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ form.department.label }}</label>
                                {{ form.department }}
                                {% if form.department.errors %}
                                    <div class="error-message">{{ form.department.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label required-field">{{ form.system_role.label }}</label>
                        {{ form.system_role }}
                        {% if form.system_role.errors %}
                            <div class="error-message">{{ form.system_role.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>


            </div>

            <!-- Sidebar with helpful information -->
            <div class="col-lg-4">
                <div class="form-container">
                    <h5 class="mb-3"><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h5>
                    <div class="alert alert-info">
                        <h6>نصائح لإنشاء موظف جديد:</h6>
                        <ul class="mb-0">
                            <li>تأكد من صحة البريد الإلكتروني</li>
                            <li>اختر كلمة مرور قوية</li>
                            <li>حدد القسم المناسب</li>
                            <li>أضف معلومات الاتصال الطارئ</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6>الحقول المطلوبة:</h6>
                        <ul class="mb-0">
                            <li>اسم المستخدم</li>
                            <li>كلمة المرور</li>
                            <li>الاسم الأول واسم العائلة</li>
                            <li>البريد الإلكتروني</li>
                            <li>دور النظام</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
            <button type="submit" class="btn btn-submit">
                <i class="fas fa-save me-2"></i>{{ submit_text }}
            </button>
            <a href="{% url 'hr:employee_list' %}" class="btn btn-cancel">
                <i class="fas fa-times me-2"></i>إلغاء
            </a>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Password strength checker
document.getElementById('id_password1').addEventListener('input', function() {
    const password = this.value;
    const strengthBar = document.getElementById('strengthBar');
    const strengthText = document.getElementById('strengthText');
    
    let strength = 0;
    let text = '';
    
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    strengthBar.className = 'strength-fill';
    
    switch(strength) {
        case 0:
        case 1:
            strengthBar.classList.add('strength-weak');
            text = 'ضعيفة';
            break;
        case 2:
            strengthBar.classList.add('strength-fair');
            text = 'متوسطة';
            break;
        case 3:
        case 4:
            strengthBar.classList.add('strength-good');
            text = 'جيدة';
            break;
        case 5:
            strengthBar.classList.add('strength-strong');
            text = 'قوية';
            break;
    }
    
    strengthText.textContent = text;
});

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
