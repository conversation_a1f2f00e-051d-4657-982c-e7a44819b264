{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title|default:"إدارة الباقة السياحية" }}{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .form-section {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #e9ecef;
    }
    
    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .form-section h4 {
        color: #495057;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #007bff;
        display: inline-block;
    }
    
    .image-preview {
        max-width: 200px;
        max-height: 150px;
        border-radius: 10px;
        margin-top: 10px;
    }
    
    .btn-action {
        padding: 12px 30px;
        border-radius: 25px;
        margin: 5px;
    }
    
    .required-field {
        color: #dc3545;
    }

    /* Religious Tourism Field Styling */
    #religious-tourism-container {
        background: linear-gradient(135deg, rgba(75, 0, 130, 0.1) 0%, rgba(75, 0, 130, 0.05) 100%);
        border: 2px solid #4B0082;
        border-radius: 10px;
        padding: 20px;
        margin-top: 15px;
        transition: all 0.3s ease;
    }

    #religious-tourism-container.show {
        animation: fadeInUp 0.5s ease;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    #religious-tourism-container .form-label {
        color: #4B0082;
        font-weight: 600;
        font-size: 1.1rem;
    }

    #religious-tourism-container .form-control {
        border: 2px solid #4B0082;
        border-radius: 8px;
    }

    #religious-tourism-container .form-control:focus {
        border-color: #6A0DAD;
        box-shadow: 0 0 0 0.2rem rgba(75, 0, 130, 0.25);
    }

    #religious-tourism-container .form-text {
        color: #4B0082;
        font-weight: 500;
    }
    }
    
    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-{% if package %}edit{% else %}plus{% endif %} text-primary me-2"></i>
                        {{ title|default:"إدارة الباقة السياحية" }}
                    </h1>
                    <p class="text-muted mb-0">
                        {% if package %}تعديل بيانات الباقة السياحية{% else %}إضافة باقة سياحية جديدة{% endif %}
                    </p>
                </div>
                <div>
                    <a href="{% url 'tours:package_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form method="post" enctype="multipart/form-data">
        {% csrf_token %}
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="form-container">
                    <div class="form-section">
                        <h4><i class="fas fa-info-circle text-primary me-2"></i>المعلومات الأساسية</h4>
                        
                        <div class="mb-3">
                            <label for="{{ form.title_ar.id_for_label }}" class="form-label">{{ form.title_ar.label }}</label>
                            {{ form.title_ar }}
                            {% if form.title_ar.errors %}
                                <div class="invalid-feedback d-block">{{ form.title_ar.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.short_description_ar.id_for_label }}" class="form-label">{{ form.short_description_ar.label }}</label>
                            {{ form.short_description_ar }}
                            {% if form.short_description_ar.errors %}
                                <div class="invalid-feedback d-block">{{ form.short_description_ar.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.detailed_description_ar.id_for_label }}" class="form-label">{{ form.detailed_description_ar.label }}</label>
                            {{ form.detailed_description_ar }}
                            {% if form.detailed_description_ar.errors %}
                                <div class="invalid-feedback d-block">{{ form.detailed_description_ar.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Classification -->
                    <div class="form-section">
                        <h4><i class="fas fa-tags text-primary me-2"></i>التصنيف</h4>
                        <div class="mb-3">
                            <label for="{{ form.category.id_for_label }}" class="form-label">{{ form.category.label }}</label>
                            {{ form.category }}
                            {% if form.category.errors %}
                                <div class="invalid-feedback d-block">{{ form.category.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- Religious Tourism Type (Conditional) -->
                        <div class="mb-3" id="religious-tourism-container" style="display: none;">
                            <label for="{{ form.religious_tourism_type.id_for_label }}" class="form-label">
                                <i class="fas fa-mosque text-success me-2"></i>{{ form.religious_tourism_type.label }}
                            </label>
                            {{ form.religious_tourism_type }}
                            {% if form.religious_tourism_type.errors %}
                                <div class="invalid-feedback d-block">{{ form.religious_tourism_type.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>حدد نوع السياحة الدينية المناسب للباقة
                            </div>
                        </div>
                    </div>

                    <!-- Duration and Capacity -->
                    <div class="form-section">
                        <h4><i class="fas fa-clock text-primary me-2"></i>المدة والسعة</h4>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="{{ form.duration_days.id_for_label }}" class="form-label">{{ form.duration_days.label }}</label>
                                    {{ form.duration_days }}
                                    {% if form.duration_days.errors %}
                                        <div class="invalid-feedback d-block">{{ form.duration_days.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="{{ form.duration_nights.id_for_label }}" class="form-label">{{ form.duration_nights.label }}</label>
                                    {{ form.duration_nights }}
                                    {% if form.duration_nights.errors %}
                                        <div class="invalid-feedback d-block">{{ form.duration_nights.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="{{ form.min_participants.id_for_label }}" class="form-label">{{ form.min_participants.label }}</label>
                                    {{ form.min_participants }}
                                    {% if form.min_participants.errors %}
                                        <div class="invalid-feedback d-block">{{ form.min_participants.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="{{ form.max_participants.id_for_label }}" class="form-label">{{ form.max_participants.label }}</label>
                                    {{ form.max_participants }}
                                    {% if form.max_participants.errors %}
                                        <div class="invalid-feedback d-block">{{ form.max_participants.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing -->
                    <div class="form-section">
                        <h4><i class="fas fa-money-bill text-primary me-2"></i>التسعير</h4>
                        <div class="mb-3">
                            <label for="{{ form.base_price.id_for_label }}" class="form-label">{{ form.base_price.label }}</label>
                            {{ form.base_price }}
                            {% if form.base_price.errors %}
                                <div class="invalid-feedback d-block">{{ form.base_price.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Inclusions and Exclusions -->
                    <div class="form-section">
                        <h4><i class="fas fa-list text-primary me-2"></i>المشمول وغير المشمول</h4>
                        <div class="mb-3">
                            <label for="{{ form.inclusions.id_for_label }}" class="form-label">{{ form.inclusions.label }}</label>
                            {{ form.inclusions }}
                            {% if form.inclusions.errors %}
                                <div class="invalid-feedback d-block">{{ form.inclusions.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            <label for="{{ form.exclusions.id_for_label }}" class="form-label">{{ form.exclusions.label }}</label>
                            {{ form.exclusions }}
                            {% if form.exclusions.errors %}
                                <div class="invalid-feedback d-block">{{ form.exclusions.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="form-section">
                        <h4><i class="fas fa-toggle-on text-primary me-2"></i>الحالة</h4>
                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {{ form.is_active.label }}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Submit Buttons -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="form-container">
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-action">
                            <i class="fas fa-save me-2"></i>حفظ الباقة
                        </button>
                        <a href="{% url 'tours:package_list' %}" class="btn btn-secondary btn-action">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                    </div>
                </div>
            </div>
        </div>


    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Function to check if category is religious tourism
    function checkReligiousTourism() {
        var categorySelect = $('#{{ form.category.id_for_label }}');
        var religiousContainer = $('#religious-tourism-container');
        var religiousField = $('#{{ form.religious_tourism_type.id_for_label }}');

        if (categorySelect.length && religiousContainer.length) {
            var selectedOption = categorySelect.find('option:selected');
            var categoryText = selectedOption.text().toLowerCase();

            // Check if the selected category contains "دينية" (religious) or "religieux" or "religious"
            if (categoryText.includes('دينية') || categoryText.includes('religieux') || categoryText.includes('religious')) {
                religiousContainer.show();
                religiousField.prop('required', true);
            } else {
                religiousContainer.hide();
                religiousField.prop('required', false);
                religiousField.val(''); // Clear the value when hidden
            }
        }
    }

    // Check on page load
    checkReligiousTourism();

    // Check when category changes
    $('#{{ form.category.id_for_label }}').on('change', function() {
        checkReligiousTourism();
    });

    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;
        var errors = [];

        // Check required fields
        if (!$('#id_title_ar').val()) {
            errors.push('العنوان بالعربية مطلوب');
            isValid = false;
        }

        if (!$('#id_base_price').val()) {
            errors.push('السعر الأساسي مطلوب');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            alert('يرجى تصحيح الأخطاء التالية:\n' + errors.join('\n'));
        }
    });

    console.log('Package form loaded');
});
</script>
{% endblock %}
