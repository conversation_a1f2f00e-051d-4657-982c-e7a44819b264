{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
.form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    text-align: center;
}

.form-section {
    margin-bottom: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.form-section h5 {
    color: #495057;
    margin-bottom: 20px;
    font-weight: 600;
}

.form-section h5 i {
    margin-left: 10px;
    color: #667eea;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.required-field::after {
    content: " *";
    color: #dc3545;
}

.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-submit {
    background: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(56, 239, 125, 0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
    margin-left: 15px;
}

.btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
    color: white;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
    display: block;
}

.help-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 5px;
}

.form-tips {
    background: linear-gradient(135deg, rgba(56, 239, 125, 0.1) 0%, rgba(17, 153, 142, 0.1) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    border-left: 4px solid #38ef7d;
}

.form-tips h6 {
    color: #11998e;
    margin-bottom: 15px;
    font-weight: 600;
}

.form-tips ul {
    margin-bottom: 0;
    padding-right: 20px;
}

.form-tips li {
    color: #495057;
    margin-bottom: 5px;
}

.leave-preview {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    border-left: 4px solid #667eea;
}

.leave-preview h6 {
    color: #667eea;
    margin-bottom: 15px;
    font-weight: 600;
}

.preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(102, 126, 234, 0.2);
}

.preview-item:last-child {
    border-bottom: none;
}

.preview-label {
    color: #6c757d;
    font-weight: 500;
}

.preview-value {
    color: #495057;
    font-weight: 600;
}

.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.file-upload-area.dragover {
    border-color: #38ef7d;
    background: rgba(56, 239, 125, 0.1);
}

.file-upload-icon {
    font-size: 3rem;
    color: #dee2e6;
    margin-bottom: 15px;
}

.file-upload-text {
    color: #6c757d;
    margin-bottom: 10px;
}

.file-upload-hint {
    color: #adb5bd;
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .form-container {
        padding: 20px;
    }
    
    .form-section {
        padding: 15px;
    }
    
    .btn-submit, .btn-cancel {
        width: 100%;
        margin-bottom: 10px;
        margin-left: 0;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="form-header">
        <h1 class="h3 mb-3">🏖️ {{ title }}</h1>
        <p class="mb-0">إدارة طلبات الإجازات والعطل</p>
    </div>

    <!-- Form Tips -->
    <div class="form-tips">
        <h6><i class="fas fa-lightbulb"></i> نصائح مهمة</h6>
        <ul>
            <li>تأكد من صحة تواريخ الإجازة قبل الإرسال</li>
            <li>اكتب سبباً واضحاً ومفصلاً لطلب الإجازة</li>
            <li>يمكن رفع وثيقة مساندة إذا لزم الأمر</li>
            <li>لا يمكن طلب إجازة في تاريخ سابق</li>
            <li>سيتم احتساب عدد الأيام تلقائياً</li>
        </ul>
    </div>

    <form method="post" enctype="multipart/form-data" id="leaveForm">
        {% csrf_token %}
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="form-section">
                    <h5><i class="fas fa-info-circle"></i>معلومات الإجازة</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.employee.label }}</label>
                                {{ form.employee }}
                                {% if form.employee.errors %}
                                    <span class="error-message">{{ form.employee.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.leave_type.label }}</label>
                                {{ form.leave_type }}
                                {% if form.leave_type.errors %}
                                    <span class="error-message">{{ form.leave_type.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dates -->
                <div class="form-section">
                    <h5><i class="fas fa-calendar"></i>التواريخ</h5>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.start_date.label }}</label>
                                {{ form.start_date }}
                                {% if form.start_date.errors %}
                                    <span class="error-message">{{ form.start_date.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.end_date.label }}</label>
                                {{ form.end_date }}
                                {% if form.end_date.errors %}
                                    <span class="error-message">{{ form.end_date.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.days_requested.label }}</label>
                                {{ form.days_requested }}
                                {% if form.days_requested.errors %}
                                    <span class="error-message">{{ form.days_requested.errors.0 }}</span>
                                {% endif %}
                                <div class="help-text">سيتم احتسابه تلقائياً</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reason -->
                <div class="form-section">
                    <h5><i class="fas fa-comment"></i>السبب والتفاصيل</h5>
                    
                    <div class="form-group">
                        <label class="form-label required-field">{{ form.reason.label }}</label>
                        {{ form.reason }}
                        {% if form.reason.errors %}
                            <span class="error-message">{{ form.reason.errors.0 }}</span>
                        {% endif %}
                        <div class="help-text">اكتب سبباً واضحاً ومفصلاً لطلب الإجازة</div>
                    </div>
                </div>

                <!-- Supporting Document -->
                <div class="form-section">
                    <h5><i class="fas fa-file-upload"></i>وثيقة مساندة</h5>
                    
                    <div class="form-group">
                        <label class="form-label">{{ form.supporting_document.label }}</label>
                        <div class="file-upload-area" onclick="document.getElementById('{{ form.supporting_document.id_for_label }}').click()">
                            <div class="file-upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="file-upload-text">انقر لاختيار وثيقة مساندة</div>
                            <div class="file-upload-hint">PDF, DOC, DOCX, JPG, PNG (اختياري)</div>
                        </div>
                        {{ form.supporting_document }}
                        {% if form.supporting_document.errors %}
                            <span class="error-message">{{ form.supporting_document.errors.0 }}</span>
                        {% endif %}
                        <div class="help-text">وثيقة مساندة للطلب (شهادة طبية، دعوة زفاف، إلخ)</div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="text-center">
                    <button type="submit" class="btn btn-submit">
                        <i class="fas fa-paper-plane"></i> {{ submit_text|default:"إرسال الطلب" }}
                    </button>
                    <a href="{% url 'hr:leave_list' %}" class="btn btn-cancel">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Leave Preview -->
                <div class="leave-preview">
                    <h6><i class="fas fa-eye"></i> معاينة الطلب</h6>
                    <div id="leavePreview">
                        <div class="preview-item">
                            <span class="preview-label">الموظف:</span>
                            <span class="preview-value" id="previewEmployee">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">نوع الإجازة:</span>
                            <span class="preview-value" id="previewType">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">تاريخ البداية:</span>
                            <span class="preview-value" id="previewStartDate">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">تاريخ النهاية:</span>
                            <span class="preview-value" id="previewEndDate">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">عدد الأيام:</span>
                            <span class="preview-value" id="previewDays">0</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">الحالة:</span>
                            <span class="preview-value" id="previewStatus">في الانتظار</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form validation and preview
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('leaveForm');
    const employeeField = document.getElementById('{{ form.employee.id_for_label }}');
    const typeField = document.getElementById('{{ form.leave_type.id_for_label }}');
    const startDateField = document.getElementById('{{ form.start_date.id_for_label }}');
    const endDateField = document.getElementById('{{ form.end_date.id_for_label }}');
    const daysField = document.getElementById('{{ form.days_requested.id_for_label }}');
    
    // Update preview
    function updatePreview() {
        document.getElementById('previewEmployee').textContent = 
            employeeField.options[employeeField.selectedIndex]?.text || 'غير محدد';
        document.getElementById('previewType').textContent = 
            typeField.options[typeField.selectedIndex]?.text || 'غير محدد';
        document.getElementById('previewStartDate').textContent = 
            startDateField.value || 'غير محدد';
        document.getElementById('previewEndDate').textContent = 
            endDateField.value || 'غير محدد';
        document.getElementById('previewDays').textContent = 
            daysField.value || '0';
    }
    
    // Calculate days automatically
    function calculateDays() {
        if (startDateField.value && endDateField.value) {
            const startDate = new Date(startDateField.value);
            const endDate = new Date(endDateField.value);
            
            if (endDate > startDate) {
                const timeDiff = endDate.getTime() - startDate.getTime();
                const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
                daysField.value = daysDiff;
                updatePreview();
            }
        }
    }
    
    // Add event listeners
    [employeeField, typeField, startDateField, endDateField, daysField].forEach(field => {
        if (field) {
            field.addEventListener('change', updatePreview);
            field.addEventListener('input', updatePreview);
        }
    });
    
    // Add date change listeners for auto-calculation
    if (startDateField && endDateField) {
        startDateField.addEventListener('change', calculateDays);
        endDateField.addEventListener('change', calculateDays);
    }
    
    // Initial preview update
    updatePreview();
    
    // File upload handling
    const fileInput = document.getElementById('{{ form.supporting_document.id_for_label }}');
    const uploadArea = document.querySelector('.file-upload-area');
    
    if (fileInput && uploadArea) {
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                uploadArea.querySelector('.file-upload-text').textContent = 
                    'تم اختيار: ' + this.files[0].name;
            }
        });
        
        // Drag and drop
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function() {
            this.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            fileInput.files = e.dataTransfer.files;
            if (fileInput.files.length > 0) {
                this.querySelector('.file-upload-text').textContent = 
                    'تم اختيار: ' + fileInput.files[0].name;
            }
        });
    }
    
    // Form validation
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        const requiredFields = [employeeField, typeField, startDateField, endDateField, daysField];
        requiredFields.forEach(field => {
            if (field && !field.value.trim()) {
                isValid = false;
                field.style.borderColor = '#dc3545';
            } else if (field) {
                field.style.borderColor = '#e9ecef';
            }
        });
        
        // Check date validation
        if (startDateField.value && endDateField.value) {
            if (new Date(startDateField.value) >= new Date(endDateField.value)) {
                isValid = false;
                endDateField.style.borderColor = '#dc3545';
                alert('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
            }
            
            // Check if start date is in the past
            if (new Date(startDateField.value) < new Date()) {
                isValid = false;
                startDateField.style.borderColor = '#dc3545';
                alert('لا يمكن طلب إجازة في تاريخ سابق');
            }
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
