"""
Forms for HR app.
"""
from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.core.exceptions import ValidationError
from django.utils import timezone
from apps.accounts.models import User
from .models import Department, Position, Leave, Attendance


class EmployeeCreateForm(UserCreationForm):
    """Form for creating new employees."""
    
    # Personal Information
    first_name = forms.CharField(
        max_length=150,
        label='الاسم الأول',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'أدخل الاسم الأول'
        })
    )
    
    last_name = forms.CharField(
        max_length=150,
        label='اسم العائلة',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'أدخل اسم العائلة'
        })
    )
    
    email = forms.EmailField(
        label='البريد الإلكتروني',
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        })
    )
    
    phone = forms.CharField(
        max_length=20,
        required=False,
        label='رقم الهاتف',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+212 6XX XXX XXX'
        })
    )
    
    # Employment Information
    employee_id = forms.CharField(
        max_length=20,
        required=False,
        label='رقم الموظف',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'EMP001'
        })
    )
    
    department = forms.CharField(
        max_length=100,
        required=False,
        label='القسم',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'اختر القسم'
        })
    )
    
    system_role = forms.ChoiceField(
        choices=User.SYSTEM_ROLE_CHOICES,
        label='دور النظام',
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    # Additional fields that exist in the User model
    first_name_ar = forms.CharField(
        max_length=50,
        required=False,
        label='الاسم الأول بالعربية',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'الاسم الأول بالعربية'
        })
    )

    last_name_ar = forms.CharField(
        max_length=50,
        required=False,
        label='اسم العائلة بالعربية',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'اسم العائلة بالعربية'
        })
    )

    hire_date = forms.DateField(
        required=False,
        label='تاريخ التوظيف',
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    class Meta:
        model = User
        fields = [
            'username', 'password1', 'password2',
            'first_name', 'last_name', 'email', 'phone',
            'employee_id', 'department', 'system_role',
            'first_name_ar', 'last_name_ar', 'hire_date'
        ]
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Customize username field
        self.fields['username'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'اسم المستخدم'
        })
        self.fields['username'].label = 'اسم المستخدم'
        
        # Customize password fields
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'كلمة المرور'
        })
        self.fields['password1'].label = 'كلمة المرور'
        
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'تأكيد كلمة المرور'
        })
        self.fields['password2'].label = 'تأكيد كلمة المرور'
        
        # Populate department choices from existing departments
        departments = Department.objects.filter(is_active=True)
        if departments.exists():
            department_choices = [(dept.name_ar, dept.name_ar) for dept in departments]
            self.fields['department'] = forms.ChoiceField(
                choices=[('', 'اختر القسم')] + department_choices,
                required=False,
                label='القسم',
                widget=forms.Select(attrs={'class': 'form-select'})
            )
    
    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email and User.objects.filter(email=email).exists():
            raise ValidationError('هذا البريد الإلكتروني مستخدم بالفعل.')
        return email
    
    def clean_employee_id(self):
        employee_id = self.cleaned_data.get('employee_id')
        if employee_id and User.objects.filter(employee_id=employee_id).exists():
            raise ValidationError('رقم الموظف هذا مستخدم بالفعل.')
        return employee_id
    
    def save(self, commit=True):
        user = super().save(commit=False)
        user.is_active = True
        if commit:
            user.save()
        return user


class EmployeeUpdateForm(forms.ModelForm):
    """Form for updating employee information."""

    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'email', 'phone',
            'employee_id', 'department', 'system_role',
            'first_name_ar', 'last_name_ar', 'hire_date',
            'is_active'
        ]
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add CSS classes to all fields
        for field_name, field in self.fields.items():
            if isinstance(field.widget, forms.Select):
                field.widget.attrs['class'] = 'form-select'
            elif isinstance(field.widget, forms.Textarea):
                field.widget.attrs['class'] = 'form-control'
                field.widget.attrs['rows'] = 3
            elif isinstance(field.widget, forms.CheckboxInput):
                field.widget.attrs['class'] = 'form-check-input'
            else:
                field.widget.attrs['class'] = 'form-control'
        
        # Set labels in Arabic
        self.fields['first_name'].label = 'الاسم الأول'
        self.fields['last_name'].label = 'اسم العائلة'
        self.fields['email'].label = 'البريد الإلكتروني'
        self.fields['phone'].label = 'رقم الهاتف'
        self.fields['employee_id'].label = 'رقم الموظف'
        self.fields['department'].label = 'القسم'
        self.fields['system_role'].label = 'دور النظام'
        self.fields['first_name_ar'].label = 'الاسم الأول بالعربية'
        self.fields['last_name_ar'].label = 'اسم العائلة بالعربية'
        self.fields['hire_date'].label = 'تاريخ التوظيف'
        self.fields['is_active'].label = 'نشط'
        
        # Set date widget
        self.fields['hire_date'].widget = forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
        
        # Populate department choices
        departments = Department.objects.filter(is_active=True)
        if departments.exists():
            department_choices = [(dept.name_ar, dept.name_ar) for dept in departments]
            self.fields['department'] = forms.ChoiceField(
                choices=[('', 'اختر القسم')] + department_choices,
                required=False,
                label='القسم',
                widget=forms.Select(attrs={'class': 'form-select'})
            )


class LeaveForm(forms.ModelForm):
    """Form for creating and editing leave requests."""

    class Meta:
        model = Leave
        fields = [
            'employee', 'leave_type', 'start_date', 'end_date',
            'days_requested', 'reason', 'supporting_document'
        ]

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Customize employee field
        self.fields['employee'].queryset = User.objects.filter(
            is_active=True,
            system_role__in=['employee', 'manager', 'admin']
        ).order_by('first_name', 'last_name')
        self.fields['employee'].widget.attrs.update({
            'class': 'form-select',
            'required': True
        })
        self.fields['employee'].label = 'الموظف'

        # Customize leave_type field
        self.fields['leave_type'].widget.attrs.update({
            'class': 'form-select',
            'required': True
        })
        self.fields['leave_type'].label = 'نوع الإجازة'

        # Customize date fields
        self.fields['start_date'].widget = forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date',
            'required': True
        })
        self.fields['start_date'].label = 'تاريخ البداية'

        self.fields['end_date'].widget = forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date',
            'required': True
        })
        self.fields['end_date'].label = 'تاريخ النهاية'

        # Customize days_requested field
        self.fields['days_requested'].widget.attrs.update({
            'class': 'form-control',
            'min': '1',
            'type': 'number',
            'required': True
        })
        self.fields['days_requested'].label = 'عدد الأيام'

        # Customize reason field
        self.fields['reason'].widget.attrs.update({
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'اكتب سبب طلب الإجازة...',
            'required': True
        })
        self.fields['reason'].label = 'السبب'

        # Customize supporting_document field
        self.fields['supporting_document'].widget.attrs.update({
            'class': 'form-control',
            'accept': '.pdf,.doc,.docx,.jpg,.jpeg,.png'
        })
        self.fields['supporting_document'].label = 'وثيقة مساندة'

        # Set initial values
        if not self.instance.pk:
            self.fields['start_date'].initial = timezone.now().date()
            if self.user:
                self.fields['employee'].initial = self.user

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        days_requested = cleaned_data.get('days_requested')

        # Validate date ranges
        if start_date and end_date:
            if start_date >= end_date:
                raise ValidationError({
                    'end_date': 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية'
                })

            # Calculate actual days
            actual_days = (end_date - start_date).days + 1
            if days_requested and days_requested != actual_days:
                cleaned_data['days_requested'] = actual_days

        # Validate start date is not in the past
        if start_date and start_date < timezone.now().date():
            raise ValidationError({
                'start_date': 'لا يمكن طلب إجازة في تاريخ سابق'
            })

        return cleaned_data

    def save(self, commit=True):
        leave = super().save(commit=False)

        # Set status to pending for new leaves
        if not leave.pk:
            leave.status = 'pending'

        if commit:
            leave.save()

        return leave


class LeaveApprovalForm(forms.ModelForm):
    """Form for approving/rejecting leave requests."""

    class Meta:
        model = Leave
        fields = ['status', 'approval_notes']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Customize status field
        self.fields['status'].choices = [
            ('approved', 'موافق عليها'),
            ('rejected', 'مرفوضة'),
        ]
        self.fields['status'].widget.attrs.update({
            'class': 'form-select',
            'required': True
        })
        self.fields['status'].label = 'القرار'

        # Customize approval_notes field
        self.fields['approval_notes'].widget.attrs.update({
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'ملاحظات حول القرار (اختياري)...'
        })
        self.fields['approval_notes'].label = 'ملاحظات'
        self.fields['approval_notes'].required = False

    def save(self, commit=True):
        leave = super().save(commit=False)

        # Set approval date and user
        if leave.status in ['approved', 'rejected']:
            leave.approval_date = timezone.now()

        if commit:
            leave.save()

        return leave
