{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}عقود الموردين{% endblock %}

{% block extra_css %}
<style>
.contract-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
}

.contract-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.contract-card.draft {
    border-left-color: #6c757d;
}

.contract-card.active {
    border-left-color: #38ef7d;
}

.contract-card.expired {
    border-left-color: #ff6b6b;
}

.contract-card.terminated {
    border-left-color: #dc3545;
}

.contract-card.suspended {
    border-left-color: #fdbb2d;
}

.supplier-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    margin-right: 15px;
}

.contract-info h5 {
    margin-bottom: 5px;
    color: #495057;
}

.contract-info p {
    margin-bottom: 3px;
    color: #6c757d;
    font-size: 0.9rem;
}

.status-badge {
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-draft {
    background: #e2e3e5;
    color: #383d41;
}

.status-active {
    background: #d1edff;
    color: #0c63e4;
}

.status-expired {
    background: #f8d7da;
    color: #721c24;
}

.status-terminated {
    background: #f8d7da;
    color: #721c24;
}

.status-suspended {
    background: #fff3cd;
    color: #856404;
}

.filter-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
}

.filter-card .form-control,
.filter-card .form-select {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    border-radius: 10px;
}

.filter-card .form-control::placeholder {
    color: rgba(255,255,255,0.7);
}

.filter-card .form-control:focus,
.filter-card .form-select:focus {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
}

.btn-filter {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    border-radius: 10px;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.btn-filter:hover {
    background: rgba(255,255,255,0.3);
    color: white;
    transform: translateY(-2px);
}

.btn-add {
    background: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-add:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(56, 239, 125, 0.4);
    color: white;
}

.contract-dates {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
    padding: 10px;
    margin-top: 10px;
}

.contract-dates strong {
    color: #667eea;
}

.contract-value {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 8px 12px;
    margin-top: 8px;
    text-align: center;
}

.contract-value .value {
    font-size: 1.1rem;
    font-weight: bold;
    color: #495057;
}

.contract-actions {
    display: flex;
    gap: 10px;
}

.btn-action {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.btn-view {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

.btn-view:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-edit {
    background: linear-gradient(135deg, #fdbb2d 0%, #f39c12 100%);
    border: none;
    color: white;
}

.btn-edit:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(253, 187, 45, 0.4);
    color: white;
}

.stats-row {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
}

.stat-item {
    text-align: center;
    padding: 15px;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.expiry-warning {
    background: #fff3cd;
    color: #856404;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    margin-top: 5px;
}

.expiry-danger {
    background: #f8d7da;
    color: #721c24;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #dee2e6;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">📄 عقود الموردين</h1>
                    <p class="text-muted">إدارة ومتابعة عقود الموردين والخدمات</p>
                </div>
                <div>
                    <a href="{% url 'suppliers:dashboard' %}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
                    </a>
                    <a href="{% url 'suppliers:contract_add' %}" class="btn btn-add">
                        <i class="fas fa-plus"></i> عقد جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-row">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ contracts|length }}</div>
                    <div class="stat-label">إجمالي العقود</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">0</div>
                    <div class="stat-label">عقود نشطة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">0</div>
                    <div class="stat-label">منتهية الصلاحية</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ page_obj.paginator.count }}</div>
                    <div class="stat-label">النتائج المعروضة</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="get" class="row align-items-end">
            <div class="col-md-3">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    {% for status_key, status_name in status_choices %}
                    <option value="{{ status_key }}" {% if request.GET.status == status_key %}selected{% endif %}>
                        {{ status_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">نوع العقد</label>
                <select name="contract_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    {% for type_key, type_name in contract_type_choices %}
                    <option value="{{ type_key }}" {% if request.GET.contract_type == type_key %}selected{% endif %}>
                        {{ type_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">المورد</label>
                <select name="supplier" class="form-select">
                    <option value="">جميع الموردين</option>
                    {% for supplier in suppliers %}
                    <option value="{{ supplier.id }}" {% if request.GET.supplier == supplier.id|stringformat:"s" %}selected{% endif %}>
                        {{ supplier.name_ar }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-filter w-100">
                    <i class="fas fa-search"></i> بحث
                </button>
                <div class="mt-2">
                    <label class="form-check-label">
                        <input type="checkbox" name="expiring" class="form-check-input" {% if request.GET.expiring %}checked{% endif %}>
                        العقود المنتهية قريباً
                    </label>
                </div>
            </div>
        </form>
    </div>

    <!-- Contract List -->
    {% if contracts %}
    <div class="row">
        {% for contract in contracts %}
        <div class="col-lg-6 col-xl-4">
            <div class="contract-card {{ contract.status }}">
                <div class="d-flex align-items-start mb-3">
                    <div class="supplier-avatar">
                        {{ contract.supplier.name_ar|first }}
                    </div>
                    <div class="contract-info flex-grow-1">
                        <h5>{{ contract.title }}</h5>
                        <p><i class="fas fa-building me-1"></i>{{ contract.supplier.name_ar }}</p>
                        <p><i class="fas fa-tag me-1"></i>{{ contract.get_contract_type_display }}</p>
                        <p><i class="fas fa-file-alt me-1"></i>{{ contract.contract_number }}</p>
                    </div>
                </div>
                
                <div class="contract-dates">
                    <div class="row text-center">
                        <div class="col-6">
                            <strong>من:</strong><br>
                            <small>{{ contract.start_date }}</small>
                        </div>
                        <div class="col-6">
                            <strong>إلى:</strong><br>
                            <small>{{ contract.end_date }}</small>
                        </div>
                    </div>
                </div>
                
                {% if contract.contract_value %}
                <div class="contract-value">
                    <div class="value">{{ contract.contract_value }} درهم</div>
                    <small class="text-muted">قيمة العقد</small>
                </div>
                {% endif %}
                
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <span class="status-badge status-{{ contract.status }}">
                        {{ contract.get_status_display }}
                    </span>
                    
                    <div class="contract-actions">
                        <a href="{% url 'suppliers:supplier_detail' contract.supplier.pk %}" class="btn btn-view btn-action" title="عرض المورد">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{% url 'suppliers:contract_edit' contract.pk %}" class="btn btn-edit btn-action" title="تعديل العقد">
                            <i class="fas fa-edit"></i>
                        </a>
                    </div>
                </div>
                
                <!-- Expiry Warning -->
                {% if contract.status == 'active' and contract.days_until_expiry <= 30 %}
                <div class="expiry-warning {% if contract.days_until_expiry <= 7 %}expiry-danger{% endif %}">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    {% if contract.days_until_expiry <= 0 %}
                        منتهي الصلاحية
                    {% elif contract.days_until_expiry == 1 %}
                        ينتهي غداً
                    {% else %}
                        ينتهي خلال {{ contract.days_until_expiry }} يوم
                    {% endif %}
                </div>
                {% endif %}
                
                {% if contract.created_by %}
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-user me-1"></i>أنشئ بواسطة: {{ contract.created_by.get_full_name }}
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Contract pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.contract_type %}contract_type={{ request.GET.contract_type }}&{% endif %}{% if request.GET.supplier %}supplier={{ request.GET.supplier }}&{% endif %}{% if request.GET.expiring %}expiring={{ request.GET.expiring }}&{% endif %}page={{ page_obj.previous_page_number }}">السابق</a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.contract_type %}contract_type={{ request.GET.contract_type }}&{% endif %}{% if request.GET.supplier %}supplier={{ request.GET.supplier }}&{% endif %}{% if request.GET.expiring %}expiring={{ request.GET.expiring }}&{% endif %}page={{ num }}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.contract_type %}contract_type={{ request.GET.contract_type }}&{% endif %}{% if request.GET.supplier %}supplier={{ request.GET.supplier }}&{% endif %}{% if request.GET.expiring %}expiring={{ request.GET.expiring }}&{% endif %}page={{ page_obj.next_page_number }}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}

    {% else %}
    <!-- Empty State -->
    <div class="empty-state">
        <i class="fas fa-file-contract"></i>
        <h4>لا توجد عقود</h4>
        <p>لم يتم العثور على أي عقود بناءً على معايير البحث المحددة.</p>
        <a href="{% url 'suppliers:contract_add' %}" class="btn btn-add mt-3">
            <i class="fas fa-plus"></i> إنشاء عقد جديد
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit form on filter change
document.querySelectorAll('select[name="status"], select[name="contract_type"], select[name="supplier"]').forEach(function(select) {
    select.addEventListener('change', function() {
        this.form.submit();
    });
});

// Handle expiring checkbox
document.querySelector('input[name="expiring"]').addEventListener('change', function() {
    this.form.submit();
});

// Clear filters
function clearFilters() {
    window.location.href = '{% url "suppliers:contract_list" %}';
}
</script>
{% endblock %}
