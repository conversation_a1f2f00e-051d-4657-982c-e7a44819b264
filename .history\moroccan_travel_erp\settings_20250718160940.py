"""
Django settings for Moroccan Travel Agency ERP project.
"""

import os
from pathlib import Path
# from decouple import config

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = (
    'django-insecure-moroccan-travel-erp-development'
    '-key-change-in-production'
)

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['localhost', '127.0.0.1']

# Application definition
DJANGO_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',
]

THIRD_PARTY_APPS = [
    'rest_framework',
    'drf_spectacular',
    'crispy_forms',
    'crispy_bootstrap5',
    # 'corsheaders',
    # 'axes',
    # 'django_otp',
    # 'django_otp.plugins.otp_totp',
    # 'django_otp.plugins.otp_static',
    # 'djmoney',
    # 'phonenumber_field',
    # 'django_countries',
    # 'modeltranslation',
    # 'rosetta',
    # 'django_celery_beat',
    # 'django_celery_results',
]

LOCAL_APPS = [
    'apps.core',
    'apps.accounts',
    'apps.crm',
    'apps.tours',
    'apps.reservations',
    'apps.finance',
    'apps.hr',
    'apps.suppliers',
    'apps.reports',
    'apps.visas',
    'apps.notifications',
    'apps.inventory',
    'apps.tasks',
    'apps.documents',
    'apps.quality',
    'apps.events',
    'apps.backup',
    'apps.integrations',
]

INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    # 'django_otp.middleware.OTPMiddleware',
    # 'axes.middleware.AxesMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'moroccan_travel_erp.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.i18n',
            ],
        },
    },
]

WSGI_APPLICATION = 'moroccan_travel_erp.wsgi.application'

# Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# PostgreSQL configuration (uncomment when PostgreSQL is available)
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.postgresql',
#         'NAME': config('DB_NAME', default='moroccan_travel_erp'),
#         'USER': config('DB_USER', default='postgres'),
#         'PASSWORD': config('DB_PASSWORD', default='postgres'),
#         'HOST': config('DB_HOST', default='localhost'),
#         'PORT': config('DB_PORT', default='5432'),
#     }
# }

# Password validation
PWD_VAL = 'django.contrib.auth.password_validation'
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': f'{PWD_VAL}.UserAttributeSimilarityValidator',
    },
    {
        'NAME': f'{PWD_VAL}.MinimumLengthValidator',
    },
    {
        'NAME': f'{PWD_VAL}.CommonPasswordValidator',
    },
    {
        'NAME': f'{PWD_VAL}.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'ar'
TIME_ZONE = 'Africa/Casablanca'
USE_I18N = True
USE_TZ = True

LANGUAGES = [
    ('ar', 'العربية'),
    ('fr', 'Français'),
    ('en', 'English'),
]

LOCALE_PATHS = [
    BASE_DIR / 'locale',
]

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Site ID for django.contrib.sites
SITE_ID = 1

# REST Framework configuration
# Define base paths to make lines shorter
REST_AUTH = 'rest_framework.authentication'
REST_PERM = 'rest_framework.permissions'
REST_THROT = 'rest_framework.throttling'

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        f'{REST_AUTH}.SessionAuthentication',
        f'{REST_AUTH}.TokenAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        f'{REST_PERM}.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': (
        'rest_framework.pagination.PageNumberPagination'
    ),
    'PAGE_SIZE': 20,
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
    'DEFAULT_THROTTLE_CLASSES': [
        f'{REST_THROT}.AnonRateThrottle',
        f'{REST_THROT}.UserRateThrottle'
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '100/hour',
        'user': '1000/hour'
    }
}

# API Documentation Settings
API_TITLE = 'Moroccan Travel Agency ERP API'
API_DESC = 'API documentation for the Moroccan Travel Agency ERP system'

SPECTACULAR_SETTINGS = {
    'TITLE': API_TITLE,
    'DESCRIPTION': API_DESC,
    'VERSION': '1.0.0',
    'SERVE_INCLUDE_SCHEMA': False,
    'COMPONENT_SPLIT_REQUEST': True,
    'SCHEMA_PATH_PREFIX': '/api/',
}

# Documentation setup is simplified for basic configuration

# Crispy Forms
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"

# Celery Configuration (disabled for basic setup)
# CELERY_BROKER_URL = config('REDIS_URL', default='redis://localhost:6379/0')
# CELERY_RESULT_BACKEND = config('REDIS_URL', default='redis://localhost:6379/0')
# CELERY_ACCEPT_CONTENT = ['json']
# CELERY_TASK_SERIALIZER = 'json'
# CELERY_RESULT_SERIALIZER = 'json'
# CELERY_TIMEZONE = TIME_ZONE

# CORS settings - define allowed origins (override in production)
CORS_ORIGINS = ["http://localhost:3000", "http://127.0.0.1:3000"]
CORS_ALLOWED_ORIGINS = CORS_ORIGINS

# Security settings
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'
SECURE_REFERRER_POLICY = 'same-origin'  # More restrictive policy

# Session security settings
SESSION_COOKIE_SECURE = False  # Enable in production (HTTPS)
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
SESSION_EXPIRE_AT_BROWSER_CLOSE = True
SESSION_COOKIE_AGE = 3600  # 1 hour timeout

# CSRF protection
CSRF_COOKIE_SECURE = False  # Set to True in production with HTTPS
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Lax'

# Password validation
# Import validation class paths to shorten lines
PWD_VAL = 'django.contrib.auth.password_validation'
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': f'{PWD_VAL}.UserAttributeSimilarityValidator',
    },
    {
        'NAME': f'{PWD_VAL}.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 8,
        }
    },
    {
        'NAME': f'{PWD_VAL}.CommonPasswordValidator',
    },
    {
        'NAME': f'{PWD_VAL}.NumericPasswordValidator',
    },
]

# Login security
LOGIN_ATTEMPTS_LIMIT = 5
LOGIN_ATTEMPTS_TIMEOUT = 300  # 5 minutes

# Logging configuration
# Logging configuration
LOG_FORMAT = '{levelname} {asctime} {module} {process:d} {thread:d} {message}'
SIMPLE_LOG_FORMAT = '{levelname} {message}'

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,

    # Log formatters
    'formatters': {
        'verbose': {
            'format': LOG_FORMAT,
            'style': '{',
        },
        'simple': {
            'format': SIMPLE_LOG_FORMAT,
            'style': '{',
        },
    },

    # Log handlers
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'django.log',
            'formatter': 'verbose',
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'errors.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },

    # Root logger
    'root': {
        'handlers': ['console'],
    },

    # Specific loggers
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'apps': {
            'handlers': ['file', 'error_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# Custom User Model
AUTH_USER_MODEL = 'accounts.User'

# Authentication backends
AUTHENTICATION_BACKENDS = [
    # 'axes.backends.AxesBackend',
    'django.contrib.auth.backends.ModelBackend',
]

# Django Axes (Login Protection)
AXES_FAILURE_LIMIT = 5
AXES_COOLOFF_TIME = 1  # 1 hour
AXES_LOCK_OUT_BY_COMBINATION_USER_AND_IP = True
AXES_RESET_ON_SUCCESS = True

# Two-Factor Authentication
OTP_TOTP_ISSUER = 'Moroccan Travel ERP'
OTP_LOGIN_URL = '/accounts/login/'

# CORS Settings
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:8080",
    "http://127.0.0.1:8080",
]

CORS_ALLOW_CREDENTIALS = True

# Translation Settings
LANGUAGES = [
    ('ar', 'العربية'),    # Arabic
    ('fr', 'Français'),   # French
    ('en', 'English'),    # English
]

# Model Translation Configuration
MT_DEFAULT_LANG = 'ar'
MT_LANGUAGES = tuple(lang[0] for lang in LANGUAGES)
MODELTRANSLATION_DEFAULT_LANGUAGE = MT_DEFAULT_LANG
MODELTRANSLATION_LANGUAGES = MT_LANGUAGES
MODELTRANSLATION_FALLBACK_LANGUAGES = MT_LANGUAGES

# Email Configuration
EMAIL_CONFIG = {
    'backend': 'django.core.mail.backends.smtp.EmailBackend',
    'host': 'smtp.gmail.com',
    'port': 587,
    'use_tls': True,
    'user': '',  # Set in .env
    'password': '',  # Set in .env
}

EMAIL_BACKEND = EMAIL_CONFIG['backend']
EMAIL_HOST = EMAIL_CONFIG['host']
EMAIL_PORT = EMAIL_CONFIG['port']
EMAIL_USE_TLS = EMAIL_CONFIG['use_tls']
EMAIL_HOST_USER = EMAIL_CONFIG['user']
EMAIL_HOST_PASSWORD = EMAIL_CONFIG['password']
DEFAULT_FROM_EMAIL = EMAIL_HOST_USER
SITE_NAME = 'Moroccan Travel ERP'

# Site URL settings
FRONTEND_URL = 'http://localhost:3000'  # Update in production



# Ensure logs directory exists
os.makedirs(BASE_DIR / 'logs', exist_ok=True)

# Cache Configuration - Using database cache for development
# For production, consider using Redis: 'django_redis.cache.RedisCache'
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
        'LOCATION': 'cache_table',
    }
}

# Redis configuration (commented out for development)
# REDIS_HOST = '127.0.0.1'
# REDIS_PORT = 6379
# REDIS_DB = 1
# REDIS_URL = f'redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}'
# CACHES = {
#     'default': {
#         'BACKEND': 'django_redis.cache.RedisCache',
#         'LOCATION': REDIS_URL,
#         'OPTIONS': {
#             'CLIENT_CLASS': 'django_redis.client.DefaultClient',
#         }
#     }
# }

# Cache timeouts
CACHE_TTL = 60 * 5  # 5 minutes
CACHE_LONG_TTL = 60 * 60  # 1 hour

# Celery configuration (disabled for basic setup)
# from celery.schedules import crontab
# CELERY_CONFIG = {
#     'broker_url': REDIS_URL,
#     'result_backend': REDIS_URL,
#     'task_serializer': 'json',
#     'result_serializer': 'json',
#     'timezone': TIME_ZONE,
# }
