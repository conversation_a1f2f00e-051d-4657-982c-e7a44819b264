{% extends 'base.html' %}
{% load static %}

{% block title %}حذف الوجهة - {{ destination.name_ar }}{% endblock %}

{% block extra_css %}
<style>
.delete-container {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    max-width: 600px;
    margin: 0 auto;
}

.delete-header {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    text-align: center;
}

.delete-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    color: rgba(255,255,255,0.9);
}

.destination-info {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    border-left: 4px solid #dc3545;
}

.destination-info h5 {
    color: #495057;
    margin-bottom: 15px;
    font-weight: 600;
}

.info-item {
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #6c757d;
    display: inline-block;
    width: 120px;
}

.info-value {
    color: #495057;
}

.warning-box {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
}

.warning-box .warning-icon {
    color: #856404;
    font-size: 1.5rem;
    margin-left: 10px;
}

.warning-text {
    color: #856404;
    font-weight: 600;
    margin-bottom: 10px;
}

.warning-details {
    color: #6c757d;
    font-size: 0.9rem;
}

.btn-delete {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
}

.btn-delete:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
    margin-left: 15px;
}

.btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
    color: white;
}

.action-buttons {
    text-align: center;
    margin-top: 30px;
}

@media (max-width: 768px) {
    .delete-container {
        padding: 20px;
        margin: 15px;
    }
    
    .delete-header {
        padding: 20px;
    }
    
    .btn-delete, .btn-cancel {
        width: 100%;
        margin-bottom: 10px;
        margin-left: 0;
    }
    
    .info-label {
        width: 100%;
        display: block;
        margin-bottom: 5px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Delete Header -->
    <div class="delete-header">
        <div class="delete-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <h1 class="h3 mb-3">تأكيد حذف الوجهة</h1>
        <p class="mb-0">هل أنت متأكد من رغبتك في حذف هذه الوجهة؟</p>
    </div>

    <div class="delete-container">
        <!-- Destination Information -->
        <div class="destination-info">
            <h5><i class="fas fa-info-circle"></i> معلومات الوجهة</h5>
            
            <div class="info-item">
                <span class="info-label">الاسم بالعربية:</span>
                <span class="info-value">{{ destination.name_ar }}</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">الاسم بالفرنسية:</span>
                <span class="info-value">{{ destination.name_fr }}</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">الاسم بالإنجليزية:</span>
                <span class="info-value">{{ destination.name_en }}</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">الدولة:</span>
                <span class="info-value">{{ destination.country.name_ar }}</span>
            </div>
            
            {% if destination.city %}
            <div class="info-item">
                <span class="info-label">المدينة:</span>
                <span class="info-value">{{ destination.city.name_ar }}</span>
            </div>
            {% endif %}
            
            <div class="info-item">
                <span class="info-label">نقاط الشعبية:</span>
                <span class="info-value">{{ destination.popularity_score }}</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">الحالة:</span>
                <span class="info-value">
                    {% if destination.is_active %}
                        <span class="badge bg-success">نشط</span>
                    {% else %}
                        <span class="badge bg-secondary">غير نشط</span>
                    {% endif %}
                </span>
            </div>
            
            <div class="info-item">
                <span class="info-label">مميز:</span>
                <span class="info-value">
                    {% if destination.is_featured %}
                        <span class="badge bg-warning">مميز</span>
                    {% else %}
                        <span class="badge bg-light text-dark">عادي</span>
                    {% endif %}
                </span>
            </div>
        </div>

        <!-- Warning Box -->
        <div class="warning-box">
            <div class="d-flex align-items-start">
                <i class="fas fa-exclamation-triangle warning-icon"></i>
                <div>
                    <div class="warning-text">تحذير: هذا الإجراء لا يمكن التراجع عنه!</div>
                    <div class="warning-details">
                        سيتم حذف الوجهة نهائياً من النظام. إذا كانت هذه الوجهة مرتبطة بباقات سياحية، 
                        فقد يؤثر ذلك على تلك الباقات. يرجى التأكد من عدم وجود ارتباطات مهمة قبل المتابعة.
                    </div>
                </div>
            </div>
        </div>

        <!-- Delete Form -->
        <form method="post">
            {% csrf_token %}
            <div class="action-buttons">
                <button type="submit" class="btn btn-delete" onclick="return confirm('هل أنت متأكد من رغبتك في حذف هذه الوجهة نهائياً؟');">
                    <i class="fas fa-trash"></i> نعم، احذف الوجهة
                </button>
                <a href="{% url 'tours:destination_detail' destination.pk %}" class="btn btn-cancel">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Destination delete confirmation loaded');
    
    // Add extra confirmation for delete button
    const deleteForm = document.querySelector('form');
    if (deleteForm) {
        deleteForm.addEventListener('submit', function(e) {
            const confirmed = confirm('تأكيد أخير: هل تريد حذف الوجهة "{{ destination.name_ar }}" نهائياً؟');
            if (!confirmed) {
                e.preventDefault();
                return false;
            }
            console.log('Destination delete confirmed');
        });
    }
});
</script>
{% endblock %}
