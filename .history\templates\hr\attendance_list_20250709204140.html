{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}سجل الحضور{% endblock %}

{% block extra_css %}
<style>
.attendance-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
}

.attendance-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.attendance-card.present {
    border-left-color: #38ef7d;
}

.attendance-card.absent {
    border-left-color: #ff6b6b;
}

.attendance-card.late {
    border-left-color: #fdbb2d;
}

.attendance-card.half_day {
    border-left-color: #667eea;
}

.attendance-card.on_leave {
    border-left-color: #6c757d;
}

.employee-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    margin-right: 15px;
}

.attendance-info h5 {
    margin-bottom: 5px;
    color: #495057;
}

.attendance-info p {
    margin-bottom: 3px;
    color: #6c757d;
    font-size: 0.9rem;
}

.status-badge {
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-present {
    background: #d1edff;
    color: #0c63e4;
}

.status-absent {
    background: #f8d7da;
    color: #721c24;
}

.status-late {
    background: #fff3cd;
    color: #856404;
}

.status-half_day {
    background: #e2e3e5;
    color: #383d41;
}

.status-on_leave {
    background: #d4edda;
    color: #155724;
}

.filter-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
}

.filter-card .form-control,
.filter-card .form-select {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    border-radius: 10px;
}

.filter-card .form-control::placeholder {
    color: rgba(255,255,255,0.7);
}

.filter-card .form-control:focus,
.filter-card .form-select:focus {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
}

.btn-filter {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    border-radius: 10px;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.btn-filter:hover {
    background: rgba(255,255,255,0.3);
    color: white;
    transform: translateY(-2px);
}

.btn-add {
    background: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-add:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(56, 239, 125, 0.4);
    color: white;
}

.btn-bulk {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.btn-bulk:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.time-info {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
    padding: 10px;
    margin-top: 10px;
}

.time-info strong {
    color: #667eea;
}

.hours-display {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}

.hours-item {
    text-align: center;
    flex: 1;
}

.hours-value {
    font-weight: bold;
    color: #667eea;
    font-size: 1.1rem;
}

.hours-label {
    font-size: 0.8rem;
    color: #6c757d;
}

.stats-row {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
}

.stat-item {
    text-align: center;
    padding: 15px;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #dee2e6;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">🕐 سجل الحضور</h1>
                    <p class="text-muted">متابعة وإدارة حضور الموظفين</p>
                </div>
                <div>
                    <a href="{% url 'hr:dashboard' %}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
                    </a>
                    <div class="btn-group">
                        <a href="{% url 'hr:attendance_add' %}" class="btn btn-add">
                            <i class="fas fa-plus"></i> تسجيل حضور
                        </a>
                        <a href="{% url 'hr:attendance_bulk' %}" class="btn btn-bulk">
                            <i class="fas fa-users"></i> تسجيل جماعي
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-row">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ attendances|length }}</div>
                    <div class="stat-label">سجلات الحضور</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">0</div>
                    <div class="stat-label">حاضر اليوم</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">0</div>
                    <div class="stat-label">غائب اليوم</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ page_obj.paginator.count }}</div>
                    <div class="stat-label">النتائج المعروضة</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="get" class="row align-items-end">
            <div class="col-md-3">
                <label class="form-label">التاريخ من</label>
                <input type="date" name="date_from" class="form-control" value="{{ request.GET.date_from }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">التاريخ إلى</label>
                <input type="date" name="date_to" class="form-control" value="{{ request.GET.date_to }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="present" {% if request.GET.status == 'present' %}selected{% endif %}>حاضر</option>
                    <option value="absent" {% if request.GET.status == 'absent' %}selected{% endif %}>غائب</option>
                    <option value="late" {% if request.GET.status == 'late' %}selected{% endif %}>متأخر</option>
                    <option value="half_day" {% if request.GET.status == 'half_day' %}selected{% endif %}>نصف يوم</option>
                    <option value="on_leave" {% if request.GET.status == 'on_leave' %}selected{% endif %}>في إجازة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الموظف</label>
                <input type="text" name="employee" class="form-control" placeholder="اسم الموظف..." value="{{ request.GET.employee }}">
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-filter w-100">
                    <i class="fas fa-search"></i> بحث
                </button>
            </div>
        </form>
    </div>

    <!-- Attendance List -->
    {% if attendances %}
    <div class="row">
        {% for attendance in attendances %}
        <div class="col-lg-6 col-xl-4">
            <div class="attendance-card {{ attendance.status }}">
                <div class="d-flex align-items-start mb-3">
                    <div class="employee-avatar">
                        {{ attendance.employee.first_name|first }}{{ attendance.employee.last_name|first }}
                    </div>
                    <div class="attendance-info flex-grow-1">
                        <h5>{{ attendance.employee.get_full_name }}</h5>
                        <p><i class="fas fa-calendar me-1"></i>{{ attendance.date }}</p>
                        <p><i class="fas fa-briefcase me-1"></i>{{ attendance.employee.get_system_role_display }}</p>
                    </div>
                </div>
                
                <div class="time-info">
                    <div class="row text-center">
                        <div class="col-6">
                            <strong>دخول:</strong><br>
                            <small>{{ attendance.check_in_time|default:"--:--" }}</small>
                        </div>
                        <div class="col-6">
                            <strong>خروج:</strong><br>
                            <small>{{ attendance.check_out_time|default:"--:--" }}</small>
                        </div>
                    </div>
                </div>
                
                <div class="hours-display">
                    <div class="hours-item">
                        <div class="hours-value">{{ attendance.scheduled_hours|default:"8.0" }}</div>
                        <div class="hours-label">مجدولة</div>
                    </div>
                    <div class="hours-item">
                        <div class="hours-value">{{ attendance.actual_hours|default:"0.0" }}</div>
                        <div class="hours-label">فعلية</div>
                    </div>
                    <div class="hours-item">
                        <div class="hours-value">{{ attendance.overtime_hours|default:"0.0" }}</div>
                        <div class="hours-label">إضافية</div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <span class="status-badge status-{{ attendance.status }}">
                        {{ attendance.get_status_display }}
                    </span>
                    
                    <div class="attendance-actions">
                        <button class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </div>
                
                {% if attendance.notes %}
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-comment me-1"></i>{{ attendance.notes|truncatechars:50 }}
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Attendance pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.date_from %}date_from={{ request.GET.date_from }}&{% endif %}{% if request.GET.date_to %}date_to={{ request.GET.date_to }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.employee %}employee={{ request.GET.employee }}&{% endif %}page={{ page_obj.previous_page_number }}">السابق</a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.date_from %}date_from={{ request.GET.date_from }}&{% endif %}{% if request.GET.date_to %}date_to={{ request.GET.date_to }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.employee %}employee={{ request.GET.employee }}&{% endif %}page={{ num }}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.date_from %}date_from={{ request.GET.date_from }}&{% endif %}{% if request.GET.date_to %}date_to={{ request.GET.date_to }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.employee %}employee={{ request.GET.employee }}&{% endif %}page={{ page_obj.next_page_number }}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}

    {% else %}
    <!-- Empty State -->
    <div class="empty-state">
        <i class="fas fa-clock"></i>
        <h4>لا توجد سجلات حضور</h4>
        <p>لم يتم العثور على أي سجلات حضور بناءً على معايير البحث المحددة.</p>
        <div class="btn-group mt-3">
            <a href="{% url 'hr:attendance_add' %}" class="btn btn-add">
                <i class="fas fa-plus"></i> تسجيل حضور جديد
            </a>
            <a href="{% url 'hr:attendance_bulk' %}" class="btn btn-bulk">
                <i class="fas fa-users"></i> تسجيل جماعي
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit form on filter change
document.querySelectorAll('select[name="status"]').forEach(function(select) {
    select.addEventListener('change', function() {
        this.form.submit();
    });
});

// Clear filters
function clearFilters() {
    window.location.href = '{% url "hr:attendance_list" %}';
}
</script>
{% endblock %}
