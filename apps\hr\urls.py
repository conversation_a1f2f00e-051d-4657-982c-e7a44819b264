"""
URL configuration for HR app.
"""
from django.urls import path
from . import views

app_name = 'hr'

urlpatterns = [
    # Dashboard
    path('', views.hr_dashboard, name='dashboard'),

    # Employees
    path('employees/', views.EmployeeListView.as_view(), name='employee_list'),
    path('employees/add/', views.EmployeeCreateView.as_view(), name='employee_add'),
    path('employees/<int:pk>/', views.EmployeeDetailView.as_view(), name='employee_detail'),
    path('employees/<int:pk>/edit/', views.EmployeeUpdateView.as_view(), name='employee_edit'),

    # Leaves
    path('leaves/', views.LeaveListView.as_view(), name='leave_list'),

    # Attendance
    path('attendance/', views.AttendanceListView.as_view(), name='attendance_list'),

    # Payroll
    path('payroll/', views.PayrollListView.as_view(), name='payroll_list'),

    # Reports
    path('reports/', views.hr_reports, name='reports'),
]
