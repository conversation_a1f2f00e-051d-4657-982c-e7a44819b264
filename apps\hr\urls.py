"""
URL configuration for HR app.
"""
from django.urls import path
from . import views

app_name = 'hr'

urlpatterns = [
    # Dashboard
    path('', views.hr_dashboard, name='dashboard'),

    # Employees
    path('employees/', views.EmployeeListView.as_view(), name='employee_list'),
    path('employees/add/', views.EmployeeCreateView.as_view(), name='employee_add'),
    path('employees/<int:pk>/', views.EmployeeDetailView.as_view(), name='employee_detail'),
    path('employees/<int:pk>/edit/', views.EmployeeUpdateView.as_view(), name='employee_edit'),

    # Leaves
    path('leaves/', views.LeaveListView.as_view(), name='leave_list'),
    path('leaves/add/', views.LeaveCreateView.as_view(), name='leave_add'),
    path('leaves/<int:pk>/', views.LeaveDetailView.as_view(), name='leave_detail'),
    path('leaves/<int:pk>/edit/', views.LeaveUpdateView.as_view(), name='leave_edit'),
    path('leaves/<int:pk>/approve/', views.LeaveApprovalView.as_view(), name='leave_approve'),

    # Attendance
    path('attendance/', views.AttendanceListView.as_view(), name='attendance_list'),
    path('attendance/add/', views.AttendanceCreateView.as_view(), name='attendance_add'),
    path('attendance/<int:pk>/', views.AttendanceDetailView.as_view(), name='attendance_detail'),
    path('attendance/<int:pk>/edit/', views.AttendanceUpdateView.as_view(), name='attendance_edit'),
    path('attendance/bulk/', views.BulkAttendanceView.as_view(), name='attendance_bulk'),

    # Payroll
    path('payroll/', views.PayrollListView.as_view(), name='payroll_list'),

    # Reports
    path('reports/', views.hr_reports, name='reports'),
]
