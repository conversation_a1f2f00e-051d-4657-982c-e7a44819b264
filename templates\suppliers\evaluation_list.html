{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}تقييمات الموردين{% endblock %}

{% block extra_css %}
<style>
.evaluation-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
}

.evaluation-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.evaluation-card.excellent {
    border-left-color: #38ef7d;
}

.evaluation-card.good {
    border-left-color: #667eea;
}

.evaluation-card.average {
    border-left-color: #fdbb2d;
}

.evaluation-card.poor {
    border-left-color: #ff6b6b;
}

.supplier-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    margin-right: 15px;
}

.evaluation-info h5 {
    margin-bottom: 5px;
    color: #495057;
}

.evaluation-info p {
    margin-bottom: 3px;
    color: #6c757d;
    font-size: 0.9rem;
}

.score-display {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin-top: 15px;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.score-item:last-child {
    margin-bottom: 0;
    padding-top: 8px;
    border-top: 2px solid rgba(102, 126, 234, 0.2);
    font-weight: bold;
}

.score-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.score-value {
    font-weight: 600;
    color: #495057;
}

.score-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.score-excellent {
    background: #d4edda;
    color: #155724;
}

.score-good {
    background: #d1edff;
    color: #0c63e4;
}

.score-average {
    background: #fff3cd;
    color: #856404;
}

.score-poor {
    background: #f8d7da;
    color: #721c24;
}

.filter-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
}

.filter-card .form-control,
.filter-card .form-select {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    border-radius: 10px;
}

.filter-card .form-control::placeholder {
    color: rgba(255,255,255,0.7);
}

.filter-card .form-control:focus,
.filter-card .form-select:focus {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
}

.btn-filter {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    border-radius: 10px;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.btn-filter:hover {
    background: rgba(255,255,255,0.3);
    color: white;
    transform: translateY(-2px);
}

.btn-add {
    background: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-add:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(56, 239, 125, 0.4);
    color: white;
}

.evaluation-actions {
    display: flex;
    gap: 10px;
}

.btn-action {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.btn-view {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

.btn-view:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.stats-row {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
}

.stat-item {
    text-align: center;
    padding: 15px;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.evaluation-period {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 8px 12px;
    margin-top: 8px;
    text-align: center;
}

.evaluation-period strong {
    color: #667eea;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #dee2e6;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">⭐ تقييمات الموردين</h1>
                    <p class="text-muted">متابعة وإدارة تقييمات أداء الموردين</p>
                </div>
                <div>
                    <a href="{% url 'suppliers:dashboard' %}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
                    </a>
                    <button class="btn btn-add">
                        <i class="fas fa-plus"></i> تقييم جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-row">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ evaluations|length }}</div>
                    <div class="stat-label">إجمالي التقييمات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">0</div>
                    <div class="stat-label">متوسط النقاط</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">0</div>
                    <div class="stat-label">تقييمات ممتازة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ page_obj.paginator.count }}</div>
                    <div class="stat-label">النتائج المعروضة</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="get" class="row align-items-end">
            <div class="col-md-4">
                <label class="form-label">المورد</label>
                <select name="supplier" class="form-select">
                    <option value="">جميع الموردين</option>
                    {% for supplier in suppliers %}
                    <option value="{{ supplier.id }}" {% if request.GET.supplier == supplier.id|stringformat:"s" %}selected{% endif %}>
                        {{ supplier.name_ar }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">النقاط من</label>
                <input type="number" name="score_min" class="form-control" placeholder="1" min="1" max="5" step="0.1" value="{{ request.GET.score_min }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">النقاط إلى</label>
                <input type="number" name="score_max" class="form-control" placeholder="5" min="1" max="5" step="0.1" value="{{ request.GET.score_max }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">تاريخ التقييم</label>
                <input type="date" name="date" class="form-control" value="{{ request.GET.date }}">
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-filter w-100">
                    <i class="fas fa-search"></i> بحث
                </button>
            </div>
        </form>
    </div>

    <!-- Evaluation List -->
    {% if evaluations %}
    <div class="row">
        {% for evaluation in evaluations %}
        <div class="col-lg-6 col-xl-4">
            <div class="evaluation-card {% if evaluation.overall_score >= 4.5 %}excellent{% elif evaluation.overall_score >= 3.5 %}good{% elif evaluation.overall_score >= 2.5 %}average{% else %}poor{% endif %}">
                <div class="d-flex align-items-start mb-3">
                    <div class="supplier-avatar">
                        {{ evaluation.supplier.name_ar|first }}
                    </div>
                    <div class="evaluation-info flex-grow-1">
                        <h5>{{ evaluation.supplier.name_ar }}</h5>
                        <p><i class="fas fa-calendar me-1"></i>{{ evaluation.evaluation_date }}</p>
                        <p><i class="fas fa-user me-1"></i>{{ evaluation.evaluated_by.get_full_name }}</p>
                        <p><i class="fas fa-building me-1"></i>{{ evaluation.supplier.get_supplier_type_display }}</p>
                    </div>
                </div>
                
                {% if evaluation.period_start and evaluation.period_end %}
                <div class="evaluation-period">
                    <strong>فترة التقييم:</strong><br>
                    <small>{{ evaluation.period_start }} - {{ evaluation.period_end }}</small>
                </div>
                {% endif %}
                
                <div class="score-display">
                    <div class="score-item">
                        <span class="score-label">الجودة:</span>
                        <span class="score-value">{{ evaluation.quality_score }}/5</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">التسليم:</span>
                        <span class="score-value">{{ evaluation.delivery_score }}/5</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">الخدمة:</span>
                        <span class="score-value">{{ evaluation.service_score }}/5</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">السعر:</span>
                        <span class="score-value">{{ evaluation.price_score }}/5</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">التواصل:</span>
                        <span class="score-value">{{ evaluation.communication_score }}/5</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">الإجمالي:</span>
                        <span class="score-badge score-{% if evaluation.overall_score >= 4.5 %}excellent{% elif evaluation.overall_score >= 3.5 %}good{% elif evaluation.overall_score >= 2.5 %}average{% else %}poor{% endif %}">
                            {{ evaluation.overall_score }}/5
                        </span>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        <small><i class="fas fa-clock me-1"></i>{{ evaluation.created_at|date:"d/m/Y" }}</small>
                    </div>
                    
                    <div class="evaluation-actions">
                        <button class="btn btn-view btn-action">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                {% if evaluation.strengths %}
                <div class="mt-2">
                    <small class="text-success">
                        <i class="fas fa-thumbs-up me-1"></i>{{ evaluation.strengths|truncatechars:50 }}
                    </small>
                </div>
                {% endif %}
                
                {% if evaluation.weaknesses %}
                <div class="mt-1">
                    <small class="text-warning">
                        <i class="fas fa-exclamation-triangle me-1"></i>{{ evaluation.weaknesses|truncatechars:50 }}
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Evaluation pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.supplier %}supplier={{ request.GET.supplier }}&{% endif %}{% if request.GET.score_min %}score_min={{ request.GET.score_min }}&{% endif %}{% if request.GET.score_max %}score_max={{ request.GET.score_max }}&{% endif %}{% if request.GET.date %}date={{ request.GET.date }}&{% endif %}page={{ page_obj.previous_page_number }}">السابق</a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.supplier %}supplier={{ request.GET.supplier }}&{% endif %}{% if request.GET.score_min %}score_min={{ request.GET.score_min }}&{% endif %}{% if request.GET.score_max %}score_max={{ request.GET.score_max }}&{% endif %}{% if request.GET.date %}date={{ request.GET.date }}&{% endif %}page={{ num }}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.supplier %}supplier={{ request.GET.supplier }}&{% endif %}{% if request.GET.score_min %}score_min={{ request.GET.score_min }}&{% endif %}{% if request.GET.score_max %}score_max={{ request.GET.score_max }}&{% endif %}{% if request.GET.date %}date={{ request.GET.date }}&{% endif %}page={{ page_obj.next_page_number }}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}

    {% else %}
    <!-- Empty State -->
    <div class="empty-state">
        <i class="fas fa-star"></i>
        <h4>لا توجد تقييمات</h4>
        <p>لم يتم العثور على أي تقييمات بناءً على معايير البحث المحددة.</p>
        <button class="btn btn-add mt-3">
            <i class="fas fa-plus"></i> إضافة تقييم جديد
        </button>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit form on filter change
document.querySelectorAll('select[name="supplier"], input[name="date"]').forEach(function(input) {
    input.addEventListener('change', function() {
        this.form.submit();
    });
});

// Clear filters
function clearFilters() {
    window.location.href = '{% url "suppliers:evaluation_list" %}';
}
</script>
{% endblock %}
