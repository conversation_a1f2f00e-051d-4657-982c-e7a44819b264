<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ report_title }} - {{ company_name }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
            direction: rtl;
            text-align: right;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
        }

        .header h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header h2 {
            color: #6c757d;
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .header .date {
            color: #6c757d;
            font-size: 1rem;
        }

        .section {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }

        .section-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }

        .stat-card h3 {
            color: #667eea;
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .stat-card p {
            color: #6c757d;
            font-size: 1rem;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table th {
            background: #667eea;
            color: white;
            padding: 15px;
            text-align: right;
            font-weight: bold;
        }

        .table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
        }

        .table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .table tr:hover {
            background: #e3f2fd;
        }

        .chart-placeholder {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            color: #6c757d;
            margin-bottom: 20px;
        }

        .summary-box {
            background: linear-gradient(135deg, rgba(56, 239, 125, 0.1) 0%, rgba(17, 153, 142, 0.1) 100%);
            border: 2px solid #38ef7d;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .summary-box h3 {
            color: #11998e;
            margin-bottom: 15px;
        }

        .summary-box ul {
            list-style: none;
            padding: 0;
        }

        .summary-box li {
            padding: 5px 0;
            border-bottom: 1px solid rgba(17, 153, 142, 0.2);
        }

        .summary-box li:last-child {
            border-bottom: none;
        }

        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .page-break {
            page-break-before: always;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            color: white;
        }

        .status-active { background-color: #38ef7d; }
        .status-inactive { background-color: #6c757d; }
        .status-preferred { background-color: #667eea; }
        .status-blacklisted { background-color: #ff6b6b; }

        .rating-stars {
            color: #ffc107;
            font-size: 1.2rem;
        }

        @media print {
            body {
                font-size: 12px;
            }
            
            .container {
                padding: 10px;
            }
            
            .section {
                margin-bottom: 20px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }
            
            .stat-card {
                padding: 10px;
            }
            
            .table th,
            .table td {
                padding: 8px;
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>{{ company_name }}</h1>
            <h2>{{ report_title }}</h2>
            <div class="date">تاريخ التقرير: {{ report_date|date:"d/m/Y" }}</div>
        </div>

        <!-- Executive Summary -->
        <div class="section">
            <div class="section-title">📊 الملخص التنفيذي</div>
            <div class="summary-box">
                <h3>نظرة عامة على الموردين</h3>
                <ul>
                    <li><strong>إجمالي الموردين:</strong> {{ supplier_stats.total_suppliers }}</li>
                    <li><strong>الموردين النشطين:</strong> {{ supplier_stats.active_suppliers }}</li>
                    <li><strong>الموردين المفضلين:</strong> {{ supplier_stats.preferred_suppliers }}</li>
                    <li><strong>إجمالي العقود النشطة:</strong> {{ contract_stats.active_contracts }}</li>
                    <li><strong>قيمة العقود الإجمالية:</strong> {{ contract_stats.total_contract_value|floatformat:2 }} درهم</li>
                    <li><strong>متوسط التقييم العام:</strong> {{ performance_stats.average_overall_score|floatformat:1 }}/5</li>
                </ul>
            </div>
        </div>

        <!-- Supplier Statistics -->
        <div class="section">
            <div class="section-title">📈 إحصائيات الموردين</div>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>{{ supplier_stats.total_suppliers }}</h3>
                    <p>إجمالي الموردين</p>
                </div>
                <div class="stat-card">
                    <h3>{{ supplier_stats.active_suppliers }}</h3>
                    <p>الموردين النشطين</p>
                </div>
                <div class="stat-card">
                    <h3>{{ supplier_stats.preferred_suppliers }}</h3>
                    <p>الموردين المفضلين</p>
                </div>
                <div class="stat-card">
                    <h3>{{ supplier_stats.blacklisted_suppliers }}</h3>
                    <p>الموردين المحظورين</p>
                </div>
            </div>

            <!-- Suppliers by Type -->
            <h3>توزيع الموردين حسب النوع</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>نوع المورد</th>
                        <th>العدد</th>
                        <th>النسبة المئوية</th>
                    </tr>
                </thead>
                <tbody>
                    {% for type_data in supplier_stats.suppliers_by_type %}
                    <tr>
                        <td>{{ type_data.supplier_type|default:"غير محدد" }}</td>
                        <td>{{ type_data.count }}</td>
                        <td>{{ type_data.count|mul:100|div:supplier_stats.total_suppliers|floatformat:1 }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Contract Statistics -->
        <div class="section page-break">
            <div class="section-title">📋 إحصائيات العقود</div>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>{{ contract_stats.total_contracts }}</h3>
                    <p>إجمالي العقود</p>
                </div>
                <div class="stat-card">
                    <h3>{{ contract_stats.active_contracts }}</h3>
                    <p>العقود النشطة</p>
                </div>
                <div class="stat-card">
                    <h3>{{ contract_stats.expired_contracts }}</h3>
                    <p>العقود المنتهية</p>
                </div>
                <div class="stat-card">
                    <h3>{{ contract_stats.expiring_soon }}</h3>
                    <p>تنتهي خلال 30 يوم</p>
                </div>
            </div>

            <!-- Recent Contracts -->
            <h3>العقود الحديثة</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>المورد</th>
                        <th>عنوان العقد</th>
                        <th>نوع العقد</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>القيمة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for contract in recent_contracts %}
                    <tr>
                        <td>{{ contract.supplier.name_ar }}</td>
                        <td>{{ contract.title }}</td>
                        <td>{{ contract.get_contract_type_display }}</td>
                        <td>{{ contract.start_date|date:"d/m/Y" }}</td>
                        <td>{{ contract.end_date|date:"d/m/Y" }}</td>
                        <td>{{ contract.contract_value|floatformat:2 }} درهم</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Performance Statistics -->
        <div class="section page-break">
            <div class="section-title">⭐ إحصائيات الأداء</div>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>{{ performance_stats.average_quality_score|floatformat:1 }}</h3>
                    <p>متوسط جودة الخدمة</p>
                </div>
                <div class="stat-card">
                    <h3>{{ performance_stats.average_delivery_score|floatformat:1 }}</h3>
                    <p>متوسط التسليم</p>
                </div>
                <div class="stat-card">
                    <h3>{{ performance_stats.average_service_score|floatformat:1 }}</h3>
                    <p>متوسط خدمة العملاء</p>
                </div>
                <div class="stat-card">
                    <h3>{{ performance_stats.average_overall_score|floatformat:1 }}</h3>
                    <p>متوسط التقييم العام</p>
                </div>
            </div>
        </div>

        <!-- Top Suppliers List -->
        <div class="section page-break">
            <div class="section-title">🏆 قائمة أفضل الموردين</div>
            <table class="table">
                <thead>
                    <tr>
                        <th>اسم المورد</th>
                        <th>النوع</th>
                        <th>التقييم</th>
                        <th>الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for supplier in suppliers_list %}
                    <tr>
                        <td>{{ supplier.name_ar }}</td>
                        <td>{{ supplier.get_supplier_type_display }}</td>
                        <td>
                            {% if supplier.rating %}
                                <span class="rating-stars">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= supplier.rating %}★{% else %}☆{% endif %}
                                    {% endfor %}
                                </span>
                                ({{ supplier.rating }}/5)
                            {% else %}
                                غير مقيم
                            {% endif %}
                        </td>
                        <td>{{ supplier.phone|default:"غير متوفر" }}</td>
                        <td>{{ supplier.email|default:"غير متوفر" }}</td>
                        <td>
                            {% if supplier.is_preferred %}
                                <span class="status-badge status-preferred">مفضل</span>
                            {% elif supplier.is_blacklisted %}
                                <span class="status-badge status-blacklisted">محظور</span>
                            {% elif supplier.is_active %}
                                <span class="status-badge status-active">نشط</span>
                            {% else %}
                                <span class="status-badge status-inactive">غير نشط</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Recent Activity -->
        <div class="section">
            <div class="section-title">📅 النشاط الحديث (آخر 30 يوم)</div>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>{{ recent_activity.new_suppliers }}</h3>
                    <p>موردين جدد</p>
                </div>
                <div class="stat-card">
                    <h3>{{ recent_activity.new_contracts }}</h3>
                    <p>عقود جديدة</p>
                </div>
                <div class="stat-card">
                    <h3>{{ recent_activity.recent_evaluations }}</h3>
                    <p>تقييمات حديثة</p>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الموارد المؤسسية - {{ company_name }}</p>
            <p>تاريخ الإنشاء: {{ report_date|date:"d/m/Y H:i" }}</p>
        </div>
    </div>
</body>
</html>
