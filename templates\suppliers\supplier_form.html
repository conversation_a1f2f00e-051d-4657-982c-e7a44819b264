{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
.form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    text-align: center;
}

.form-section {
    margin-bottom: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.form-section h5 {
    color: #495057;
    margin-bottom: 20px;
    font-weight: 600;
}

.form-section h5 i {
    margin-left: 10px;
    color: #667eea;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.required-field::after {
    content: " *";
    color: #dc3545;
}

.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-submit {
    background: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(56, 239, 125, 0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
    margin-left: 15px;
}

.btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
    color: white;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
    display: block;
}

.help-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 5px;
}

.form-tips {
    background: linear-gradient(135deg, rgba(56, 239, 125, 0.1) 0%, rgba(17, 153, 142, 0.1) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    border-left: 4px solid #38ef7d;
}

.form-tips h6 {
    color: #11998e;
    margin-bottom: 15px;
    font-weight: 600;
}

.form-tips ul {
    margin-bottom: 0;
    padding-right: 20px;
}

.form-tips li {
    color: #495057;
    margin-bottom: 5px;
}

.supplier-preview {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    border-left: 4px solid #667eea;
}

.supplier-preview h6 {
    color: #667eea;
    margin-bottom: 15px;
    font-weight: 600;
}

.preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(102, 126, 234, 0.2);
}

.preview-item:last-child {
    border-bottom: none;
}

.preview-label {
    color: #6c757d;
    font-weight: 500;
}

.preview-value {
    color: #495057;
    font-weight: 600;
}

@media (max-width: 768px) {
    .form-container {
        padding: 20px;
    }
    
    .form-section {
        padding: 15px;
    }
    
    .btn-submit, .btn-cancel {
        width: 100%;
        margin-bottom: 10px;
        margin-left: 0;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="form-header">
        <h1 class="h3 mb-3">🏢 {{ title }}</h1>
        <p class="mb-0">إضافة وإدارة موردي الخدمات والمنتجات</p>
    </div>

    <!-- Form Tips -->
    <div class="form-tips">
        <h6><i class="fas fa-lightbulb"></i> نصائح مهمة</h6>
        <ul>
            <li>الاسم بالعربية مطلوب ويجب أن يكون واضحاً ومفهوماً</li>
            <li>نوع المورد يساعد في تصنيف وتنظيم الموردين</li>
            <li>معلومات الاتصال مهمة للتواصل المستقبلي</li>
            <li>الشخص المسؤول يجب أن يكون نقطة الاتصال الرئيسية</li>
            <li>يمكن إضافة ملاحظات إضافية في حقل الملاحظات</li>
        </ul>
    </div>

    <form method="post" id="supplierForm">
        {% csrf_token %}
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="form-section">
                    <h5><i class="fas fa-info-circle"></i>المعلومات الأساسية</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.name_ar.label }}</label>
                                {{ form.name_ar }}
                                {% if form.name_ar.errors %}
                                    <span class="error-message">{{ form.name_ar.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.supplier_type.label }}</label>
                                {{ form.supplier_type }}
                                {% if form.supplier_type.errors %}
                                    <span class="error-message">{{ form.supplier_type.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ form.name_fr.label }}</label>
                                {{ form.name_fr }}
                                {% if form.name_fr.errors %}
                                    <span class="error-message">{{ form.name_fr.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ form.name_en.label }}</label>
                                {{ form.name_en }}
                                {% if form.name_en.errors %}
                                    <span class="error-message">{{ form.name_en.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="form-section">
                    <h5><i class="fas fa-phone"></i>معلومات الاتصال</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ form.email.label }}</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <span class="error-message">{{ form.email.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.phone.label }}</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <span class="error-message">{{ form.phone.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">{{ form.website.label }}</label>
                        {{ form.website }}
                        {% if form.website.errors %}
                            <span class="error-message">{{ form.website.errors.0 }}</span>
                        {% endif %}
                        <div class="help-text">الموقع الإلكتروني للمورد (اختياري)</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label class="form-label">{{ form.address.label }}</label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                    <span class="error-message">{{ form.address.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">{{ form.city.label }}</label>
                                {{ form.city }}
                                {% if form.city.errors %}
                                    <span class="error-message">{{ form.city.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Person -->
                <div class="form-section">
                    <h5><i class="fas fa-user"></i>الشخص المسؤول</h5>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.contact_person.label }}</label>
                                {{ form.contact_person }}
                                {% if form.contact_person.errors %}
                                    <span class="error-message">{{ form.contact_person.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">{{ form.contact_phone.label }}</label>
                                {{ form.contact_phone }}
                                {% if form.contact_phone.errors %}
                                    <span class="error-message">{{ form.contact_phone.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">{{ form.contact_email.label }}</label>
                                {{ form.contact_email }}
                                {% if form.contact_email.errors %}
                                    <span class="error-message">{{ form.contact_email.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="form-section">
                    <h5><i class="fas fa-clipboard"></i>معلومات إضافية</h5>
                    
                    <div class="form-group">
                        <label class="form-label">{{ form.services_offered.label }}</label>
                        {{ form.services_offered }}
                        {% if form.services_offered.errors %}
                            <span class="error-message">{{ form.services_offered.errors.0 }}</span>
                        {% endif %}
                        <div class="help-text">وصف الخدمات أو المنتجات التي يقدمها المورد</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">{{ form.payment_terms.label }}</label>
                        {{ form.payment_terms }}
                        {% if form.payment_terms.errors %}
                            <span class="error-message">{{ form.payment_terms.errors.0 }}</span>
                        {% endif %}
                        <div class="help-text">شروط الدفع المتفق عليها مع المورد</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">{{ form.notes.label }}</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <span class="error-message">{{ form.notes.errors.0 }}</span>
                        {% endif %}
                        <div class="help-text">ملاحظات إضافية حول المورد</div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="text-center">
                    <button type="submit" class="btn btn-submit">
                        <i class="fas fa-save"></i> {{ submit_text|default:"حفظ المورد" }}
                    </button>
                    <a href="{% url 'suppliers:supplier_list' %}" class="btn btn-cancel">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Supplier Preview -->
                <div class="supplier-preview">
                    <h6><i class="fas fa-eye"></i> معاينة المورد</h6>
                    <div id="supplierPreview">
                        <div class="preview-item">
                            <span class="preview-label">الاسم:</span>
                            <span class="preview-value" id="previewName">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">النوع:</span>
                            <span class="preview-value" id="previewType">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">الهاتف:</span>
                            <span class="preview-value" id="previewPhone">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">البريد:</span>
                            <span class="preview-value" id="previewEmail">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">المدينة:</span>
                            <span class="preview-value" id="previewCity">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">الشخص المسؤول:</span>
                            <span class="preview-value" id="previewContact">غير محدد</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form validation and preview
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('supplierForm');
    const nameField = document.getElementById('{{ form.name_ar.id_for_label }}');
    const typeField = document.getElementById('{{ form.supplier_type.id_for_label }}');
    const phoneField = document.getElementById('{{ form.phone.id_for_label }}');
    const emailField = document.getElementById('{{ form.email.id_for_label }}');
    const cityField = document.getElementById('{{ form.city.id_for_label }}');
    const contactField = document.getElementById('{{ form.contact_person.id_for_label }}');
    
    // Update preview
    function updatePreview() {
        document.getElementById('previewName').textContent = 
            nameField.value || 'غير محدد';
        document.getElementById('previewType').textContent = 
            typeField.options[typeField.selectedIndex]?.text || 'غير محدد';
        document.getElementById('previewPhone').textContent = 
            phoneField.value || 'غير محدد';
        document.getElementById('previewEmail').textContent = 
            emailField.value || 'غير محدد';
        document.getElementById('previewCity').textContent = 
            cityField.value || 'غير محدد';
        document.getElementById('previewContact').textContent = 
            contactField.value || 'غير محدد';
    }
    
    // Add event listeners
    [nameField, typeField, phoneField, emailField, cityField, contactField].forEach(field => {
        if (field) {
            field.addEventListener('change', updatePreview);
            field.addEventListener('input', updatePreview);
        }
    });
    
    // Initial preview update
    updatePreview();
    
    // Form validation
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        const requiredFields = [nameField, typeField, phoneField, contactField];
        requiredFields.forEach(field => {
            if (field && !field.value.trim()) {
                isValid = false;
                field.style.borderColor = '#dc3545';
            } else if (field) {
                field.style.borderColor = '#e9ecef';
            }
        });
        
        // Email validation
        if (emailField.value && !emailField.value.includes('@')) {
            isValid = false;
            emailField.style.borderColor = '#dc3545';
            alert('يرجى إدخال بريد إلكتروني صحيح');
        }
        
        if (!isValid) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
        }
    });
});
</script>
{% endblock %}
