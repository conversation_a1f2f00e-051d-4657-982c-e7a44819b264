"""
Forms for Tours app.
"""
from django import forms
from django.core.exceptions import ValidationError
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Row, Column, Submit, HTML, Div, Field
from crispy_forms.bootstrap import TabHolder, Tab
from .models import TourPackage, TourCategory, Destination


class TourPackageForm(forms.ModelForm):
    """Form for creating and updating tour packages."""

    class Meta:
        model = TourPackage
        fields = [
            'title_ar', 'title_fr', 'title_en',
            'short_description_ar', 'short_description_fr', 'short_description_en',
            'detailed_description_ar', 'detailed_description_fr', 'detailed_description_en',
            'category', 'destinations', 'duration_days', 'duration_nights',
            'max_participants', 'min_participants',
            'base_price', 'child_price', 'infant_price',
            'inclusions', 'exclusions', 'requirements',
            'age_restrictions', 'fitness_level',
            'is_active', 'is_featured', 'is_bestseller',
            'difficulty_level', 'physical_rating', 'main_image'
        ]
        widgets = {
            'title_ar': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'عنوان الباقة بالعربية'}),
            'title_fr': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Titre du package en français'}),
            'title_en': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Package Title in English'}),
            'short_description_ar': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'short_description_fr': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'short_description_en': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'detailed_description_ar': forms.Textarea(attrs={'rows': 5, 'class': 'form-control'}),
            'detailed_description_fr': forms.Textarea(attrs={'rows': 5, 'class': 'form-control'}),
            'detailed_description_en': forms.Textarea(attrs={'rows': 5, 'class': 'form-control'}),
            'duration_days': forms.NumberInput(attrs={'class': 'form-control', 'min': 1, 'max': 365}),
            'duration_nights': forms.NumberInput(attrs={'class': 'form-control', 'min': 0, 'max': 364}),
            'max_participants': forms.NumberInput(attrs={'class': 'form-control', 'min': 1, 'max': 100}),
            'min_participants': forms.NumberInput(attrs={'class': 'form-control', 'min': 1, 'max': 100}),
            'base_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': 0}),
            'child_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': 0}),
            'infant_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': 0}),
            'inclusions': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'exclusions': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'requirements': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'age_restrictions': forms.TextInput(attrs={'class': 'form-control'}),
            'fitness_level': forms.TextInput(attrs={'class': 'form-control'}),
            'physical_rating': forms.NumberInput(attrs={'class': 'form-control', 'min': 1, 'max': 5}),
            'main_image': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Make required fields
        self.fields['title_ar'].required = True
        self.fields['category'].required = True
        self.fields['destinations'].required = True
        self.fields['duration_days'].required = True
        self.fields['base_price'].required = True

        # Set up crispy forms
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_enctype = 'multipart/form-data'
        self.helper.form_class = 'form-horizontal'

        self.helper.layout = Layout(
            TabHolder(
                Tab('المعلومات الأساسية',
                    HTML('<h5 class="mb-4"><i class="fas fa-info-circle me-2"></i>معلومات الباقة</h5>'),
                    Row(
                        Column('title_ar', css_class='form-group col-md-4 mb-3'),
                        Column('title_fr', css_class='form-group col-md-4 mb-3'),
                        Column('title_en', css_class='form-group col-md-4 mb-3'),
                        css_class='form-row'
                    ),
                    Row(
                        Column('short_description_ar', css_class='form-group col-md-6 mb-3'),
                        Column('short_description_fr', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                    'short_description_en',
                    Row(
                        Column('category', css_class='form-group col-md-4 mb-3'),
                        Column('destinations', css_class='form-group col-md-4 mb-3'),
                        Column('duration_days', css_class='form-group col-md-4 mb-3'),
                        css_class='form-row'
                    ),
                    Row(
                        Column('duration_nights', css_class='form-group col-md-6 mb-3'),
                        Column('main_image', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                ),

                Tab('التسعير والمشاركين',
                    HTML('<h5 class="mb-4"><i class="fas fa-money-bill me-2"></i>معلومات التسعير</h5>'),
                    Row(
                        Column('price_per_person', css_class='form-group col-md-4 mb-3'),
                        Column('child_price', css_class='form-group col-md-4 mb-3'),
                        Column('infant_price', css_class='form-group col-md-4 mb-3'),
                        css_class='form-row'
                    ),
                    Row(
                        Column('max_participants', css_class='form-group col-md-6 mb-3'),
                        Column('difficulty_level', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                    'season',
                ),

                Tab('تفاصيل الباقة',
                    HTML('<h5 class="mb-4"><i class="fas fa-list me-2"></i>تفاصيل الخدمات</h5>'),
                    'includes',
                    'excludes',
                    HTML('<h5 class="mb-4 mt-4"><i class="fas fa-route me-2"></i>البرنامج</h5>'),
                    'itinerary',
                ),

                Tab('الشروط والسياسات',
                    HTML('<h5 class="mb-4"><i class="fas fa-file-contract me-2"></i>المتطلبات والشروط</h5>'),
                    'requirements',
                    Row(
                        Column('age_restrictions', css_class='form-group col-md-6 mb-3'),
                        Column('fitness_level', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                    HTML('<h5 class="mb-4 mt-4"><i class="fas fa-cog me-2"></i>إعدادات الباقة</h5>'),
                    Row(
                        Column('is_active', css_class='form-group col-md-6 mb-3'),
                        Column('is_featured', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                    Row(
                        Column('is_bestseller', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                ),
            ),

            HTML('<hr>'),
            Div(
                Submit('submit', 'حفظ الباقة', css_class='btn btn-primary btn-lg me-2'),
                HTML('<a href="{% url "tours:package_list" %}" class="btn btn-secondary btn-lg">إلغاء</a>'),
                css_class='text-center mt-4'
            )
        )

    def clean_base_price(self):
        """Validate base price."""
        price = self.cleaned_data.get('base_price')
        if price and price <= 0:
            raise ValidationError('السعر الأساسي يجب أن يكون أكبر من صفر')
        return price

    def clean_child_price(self):
        """Validate child price."""
        child_price = self.cleaned_data.get('child_price')
        base_price = self.cleaned_data.get('base_price')

        if child_price and base_price and child_price > base_price:
            raise ValidationError('سعر الطفل لا يمكن أن يكون أكبر من السعر الأساسي')

        return child_price

    def clean_infant_price(self):
        """Validate infant price."""
        infant_price = self.cleaned_data.get('infant_price')
        child_price = self.cleaned_data.get('child_price')

        if infant_price and child_price and infant_price > child_price:
            raise ValidationError('سعر الرضيع لا يمكن أن يكون أكبر من سعر الطفل')

        return infant_price

    def clean_duration_days(self):
        """Validate duration."""
        duration = self.cleaned_data.get('duration_days')
        if duration and duration <= 0:
            raise ValidationError('مدة الرحلة يجب أن تكون أكبر من صفر')
        return duration


class TourPackageSearchForm(forms.Form):
    """Form for searching tour packages."""

    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالعنوان أو الوصف...',
        })
    )

    category = forms.ModelChoiceField(
        queryset=TourCategory.objects.all(),
        required=False,
        empty_label='جميع الفئات',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    destination = forms.ModelChoiceField(
        queryset=Destination.objects.all(),
        required=False,
        empty_label='جميع الوجهات',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    is_active = forms.ChoiceField(
        choices=[('', 'الكل'), (True, 'نشطة'), (False, 'غير نشطة')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.form_class = 'form-inline'
        self.helper.layout = Layout(
            Row(
                Column('search', css_class='form-group col-md-4 mb-2'),
                Column('category', css_class='form-group col-md-2 mb-2'),
                Column('destination', css_class='form-group col-md-2 mb-2'),
                Column('is_active', css_class='form-group col-md-2 mb-2'),
                Column(
                    Submit('submit', 'بحث', css_class='btn btn-primary'),
                    css_class='form-group col-md-2 mb-2'
                ),
                css_class='form-row'
            )
        )


class TourPackageQuickAddForm(forms.ModelForm):
    """Quick form for adding basic package information."""

    class Meta:
        model = TourPackage
        fields = ['title_ar', 'category', 'destinations', 'duration_days', 'base_price']
        widgets = {
            'title_ar': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'عنوان الباقة'}),
            'duration_days': forms.NumberInput(attrs={'class': 'form-control', 'min': 1}),
            'base_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': 0}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Make all fields required
        for field in self.fields:
            self.fields[field].required = True

        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            'title_ar',
            Row(
                Column('category', css_class='form-group col-md-6 mb-3'),
                Column('destinations', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),
            Row(
                Column('duration_days', css_class='form-group col-md-6 mb-3'),
                Column('base_price', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),
            Submit('submit', 'إضافة باقة', css_class='btn btn-success btn-block mt-3')
        )
