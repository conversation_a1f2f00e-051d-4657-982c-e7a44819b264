"""
HR views for the Moroccan Travel Agency ERP system.
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Q, Avg
from django.utils import timezone
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from datetime import datetime, timedelta
from .models import Department, Position, Schedule, Leave, Attendance, Payroll
from apps.accounts.models import User


@login_required
def hr_dashboard(request):
    """HR dashboard view."""
    today = timezone.now().date()
    current_month = today.replace(day=1)

    # Employee statistics
    stats = {
        'total_employees': User.objects.filter(is_active=True).count(),
        'total_departments': Department.objects.filter(is_active=True).count(),
        'total_positions': Position.objects.filter(is_active=True).count(),
        'pending_leaves': Leave.objects.filter(status='pending').count(),
        'employees_on_leave': Leave.objects.filter(
            status='approved',
            start_date__lte=today,
            end_date__gte=today
        ).count(),
        'monthly_attendance_rate': 0,  # Will calculate below
        'total_payroll': Payroll.objects.filter(
            pay_period_end__gte=current_month,
            status='paid'
        ).aggregate(total=Sum('net_salary'))['total'] or 0,
    }

    # Calculate attendance rate
    total_working_days = Attendance.objects.filter(
        date__gte=current_month
    ).count()
    present_days = Attendance.objects.filter(
        date__gte=current_month,
        status='present'
    ).count()

    if total_working_days > 0:
        stats['monthly_attendance_rate'] = round((present_days / total_working_days) * 100, 1)

    # Recent leaves
    recent_leaves = Leave.objects.select_related('employee').order_by('-created_at')[:5]

    # Upcoming leaves
    upcoming_leaves = Leave.objects.filter(
        status='approved',
        start_date__gte=today,
        start_date__lte=today + timedelta(days=14)
    ).select_related('employee').order_by('start_date')[:10]

    # Department distribution
    department_stats = []
    for dept in Department.objects.filter(is_active=True):
        employee_count = User.objects.filter(
            is_active=True,
            department=dept.name_ar
        ).count()
        dept.employee_count = employee_count
        department_stats.append(dept)

    # Sort by employee count
    department_stats.sort(key=lambda x: x.employee_count, reverse=True)

    # Attendance trends
    attendance_data = []
    for i in range(7):
        date = today - timedelta(days=i)
        total = Attendance.objects.filter(date=date).count()
        present = Attendance.objects.filter(date=date, status='present').count()
        rate = round((present / total * 100) if total > 0 else 0, 1)
        attendance_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'rate': rate
        })

    context = {
        'stats': stats,
        'recent_leaves': recent_leaves,
        'upcoming_leaves': upcoming_leaves,
        'department_stats': department_stats,
        'attendance_data': list(reversed(attendance_data)),
    }

    return render(request, 'hr/dashboard.html', context)


class EmployeeListView(LoginRequiredMixin, ListView):
    """List view for employees."""
    model = User
    template_name = 'hr/employee_list.html'
    context_object_name = 'employees'
    paginate_by = 20

    def get_queryset(self):
        queryset = User.objects.filter(is_active=True).select_related(
            'department', 'position'
        ).order_by('last_name', 'first_name')

        # Filter by department
        department = self.request.GET.get('department')
        if department:
            queryset = queryset.filter(department_id=department)

        # Filter by position
        position = self.request.GET.get('position')
        if position:
            queryset = queryset.filter(position_id=position)

        # Search
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(email__icontains=search) |
                Q(employee_id__icontains=search)
            )

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['departments'] = Department.objects.filter(is_active=True)
        context['positions'] = Position.objects.filter(is_active=True)
        return context


class LeaveListView(LoginRequiredMixin, ListView):
    """List view for leaves."""
    model = Leave
    template_name = 'hr/leave_list.html'
    context_object_name = 'leaves'
    paginate_by = 20

    def get_queryset(self):
        queryset = Leave.objects.select_related('employee').order_by('-start_date')

        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by leave type
        leave_type = self.request.GET.get('leave_type')
        if leave_type:
            queryset = queryset.filter(leave_type=leave_type)

        # Filter by employee
        employee = self.request.GET.get('employee')
        if employee:
            queryset = queryset.filter(employee_id=employee)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Leave.STATUS_CHOICES
        context['leave_type_choices'] = Leave.LEAVE_TYPE_CHOICES
        context['employees'] = User.objects.filter(is_active=True)
        return context


class AttendanceListView(LoginRequiredMixin, ListView):
    """List view for attendance."""
    model = Attendance
    template_name = 'hr/attendance_list.html'
    context_object_name = 'attendances'
    paginate_by = 20

    def get_queryset(self):
        queryset = Attendance.objects.select_related('employee').order_by('-date', 'employee')

        # Filter by date range
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        if date_from:
            queryset = queryset.filter(date__gte=date_from)
        if date_to:
            queryset = queryset.filter(date__lte=date_to)

        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by employee
        employee = self.request.GET.get('employee')
        if employee:
            queryset = queryset.filter(employee_id=employee)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Attendance.STATUS_CHOICES
        context['employees'] = User.objects.filter(is_active=True)
        return context


class PayrollListView(LoginRequiredMixin, ListView):
    """List view for payroll."""
    model = Payroll
    template_name = 'hr/payroll_list.html'
    context_object_name = 'payrolls'
    paginate_by = 20

    def get_queryset(self):
        queryset = Payroll.objects.select_related('employee').order_by('-pay_period_end', 'employee')

        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by pay period
        period = self.request.GET.get('period')
        if period:
            try:
                year, month = period.split('-')
                queryset = queryset.filter(
                    pay_period_end__year=int(year),
                    pay_period_end__month=int(month)
                )
            except ValueError:
                pass

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Payroll.STATUS_CHOICES
        return context


@login_required
def hr_reports(request):
    """HR reports view."""
    today = timezone.now().date()
    current_month = today.replace(day=1)

    # Employee statistics
    employee_stats = {
        'total_employees': User.objects.filter(is_active=True).count(),
        'new_employees': User.objects.filter(
            date_joined__gte=current_month,
            is_active=True
        ).count(),
        'employees_by_department': [],  # Will be populated below
    }

    # Populate employees by department
    employees_by_department = []
    for dept in Department.objects.filter(is_active=True):
        employee_count = User.objects.filter(
            is_active=True,
            department=dept.name_ar
        ).count()
        dept.employee_count = employee_count
        employees_by_department.append(dept)

    # Sort by employee count
    employees_by_department.sort(key=lambda x: x.employee_count, reverse=True)
    employee_stats['employees_by_department'] = employees_by_department

    # Leave statistics
    leave_stats = {
        'total_leaves': Leave.objects.count(),
        'pending_leaves': Leave.objects.filter(status='pending').count(),
        'approved_leaves': Leave.objects.filter(status='approved').count(),
        'monthly_leaves': Leave.objects.filter(
            start_date__gte=current_month
        ).count(),
        'leave_by_type': Leave.objects.values('leave_type').annotate(
            count=Count('id')
        ).order_by('-count'),
    }

    # Attendance statistics
    attendance_stats = {
        'total_attendance_records': Attendance.objects.count(),
        'monthly_attendance_rate': 0,
        'average_working_hours': Attendance.objects.filter(
            date__gte=current_month
        ).aggregate(avg=Avg('actual_hours'))['avg'] or 0,
        'total_overtime_hours': Attendance.objects.filter(
            date__gte=current_month
        ).aggregate(total=Sum('overtime_hours'))['total'] or 0,
    }

    # Calculate monthly attendance rate
    total_working_days = Attendance.objects.filter(date__gte=current_month).count()
    present_days = Attendance.objects.filter(
        date__gte=current_month,
        status='present'
    ).count()

    if total_working_days > 0:
        attendance_stats['monthly_attendance_rate'] = round(
            (present_days / total_working_days) * 100, 1
        )

    # Payroll statistics
    payroll_stats = {
        'total_payroll': Payroll.objects.filter(
            pay_period_end__gte=current_month,
            status='paid'
        ).aggregate(total=Sum('net_salary'))['total'] or 0,
        'average_salary': Payroll.objects.filter(
            pay_period_end__gte=current_month,
            status='paid'
        ).aggregate(avg=Avg('net_salary'))['avg'] or 0,
        'total_overtime_pay': Payroll.objects.filter(
            pay_period_end__gte=current_month,
            status='paid'
        ).aggregate(total=Sum('overtime_amount'))['total'] or 0,
    }

    context = {
        'employee_stats': employee_stats,
        'leave_stats': leave_stats,
        'attendance_stats': attendance_stats,
        'payroll_stats': payroll_stats,
    }

    return render(request, 'hr/reports.html', context)
