{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}قائمة الإجازات{% endblock %}

{% block extra_css %}
<style>
.leave-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
}

.leave-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.leave-card.pending {
    border-left-color: #fdbb2d;
}

.leave-card.approved {
    border-left-color: #38ef7d;
}

.leave-card.rejected {
    border-left-color: #ff6b6b;
}

.leave-card.cancelled {
    border-left-color: #6c757d;
}

.employee-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    margin-right: 15px;
}

.leave-info h5 {
    margin-bottom: 5px;
    color: #495057;
}

.leave-info p {
    margin-bottom: 3px;
    color: #6c757d;
    font-size: 0.9rem;
}

.status-badge {
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-approved {
    background: #d1edff;
    color: #0c63e4;
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
}

.status-cancelled {
    background: #e2e3e5;
    color: #383d41;
}

.filter-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
}

.filter-card .form-control,
.filter-card .form-select {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    border-radius: 10px;
}

.filter-card .form-control::placeholder {
    color: rgba(255,255,255,0.7);
}

.filter-card .form-control:focus,
.filter-card .form-select:focus {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
}

.btn-filter {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    border-radius: 10px;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.btn-filter:hover {
    background: rgba(255,255,255,0.3);
    color: white;
    transform: translateY(-2px);
}

.btn-add {
    background: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-add:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(56, 239, 125, 0.4);
    color: white;
}

.leave-actions {
    display: flex;
    gap: 10px;
}

.btn-action {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.btn-view {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

.btn-view:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-approve {
    background: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
    border: none;
    color: white;
}

.btn-approve:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(56, 239, 125, 0.4);
    color: white;
}

.btn-reject {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    border: none;
    color: white;
}

.btn-reject:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
    color: white;
}

.stats-row {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
}

.stat-item {
    text-align: center;
    padding: 15px;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.leave-dates {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
    padding: 10px;
    margin-top: 10px;
}

.leave-dates strong {
    color: #667eea;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #dee2e6;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">📅 قائمة الإجازات</h1>
                    <p class="text-muted">إدارة ومتابعة طلبات الإجازات</p>
                </div>
                <div>
                    <a href="{% url 'hr:dashboard' %}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
                    </a>
                    <a href="{% url 'hr:leave_add' %}" class="btn btn-add">
                        <i class="fas fa-plus"></i> طلب إجازة جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-row">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ leaves|length }}</div>
                    <div class="stat-label">إجمالي الطلبات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ leaves|length }}</div>
                    <div class="stat-label">في الانتظار</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">0</div>
                    <div class="stat-label">موافق عليها</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ page_obj.paginator.count }}</div>
                    <div class="stat-label">النتائج المعروضة</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="get" class="row align-items-end">
            <div class="col-md-3">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    {% for status_key, status_name in status_choices %}
                    <option value="{{ status_key }}" {% if request.GET.status == status_key %}selected{% endif %}>
                        {{ status_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">نوع الإجازة</label>
                <select name="leave_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    {% for type_key, type_name in leave_type_choices %}
                    <option value="{{ type_key }}" {% if request.GET.leave_type == type_key %}selected{% endif %}>
                        {{ type_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">الموظف</label>
                <select name="employee" class="form-select">
                    <option value="">جميع الموظفين</option>
                    {% for employee in employees %}
                    <option value="{{ employee.id }}" {% if request.GET.employee == employee.id|stringformat:"s" %}selected{% endif %}>
                        {{ employee.get_full_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-filter w-100">
                    <i class="fas fa-search"></i> بحث
                </button>
            </div>
        </form>
    </div>

    <!-- Leave List -->
    {% if leaves %}
    <div class="row">
        {% for leave in leaves %}
        <div class="col-lg-6 col-xl-4">
            <div class="leave-card {{ leave.status }}">
                <div class="d-flex align-items-start mb-3">
                    <div class="employee-avatar">
                        {{ leave.employee.first_name|first }}{{ leave.employee.last_name|first }}
                    </div>
                    <div class="leave-info flex-grow-1">
                        <h5>{{ leave.employee.get_full_name }}</h5>
                        <p><i class="fas fa-calendar-alt me-1"></i>{{ leave.get_leave_type_display }}</p>
                        <p><i class="fas fa-clock me-1"></i>{{ leave.days_requested }} يوم</p>
                        <p><i class="fas fa-comment me-1"></i>{{ leave.reason|truncatechars:50 }}</p>
                    </div>
                </div>
                
                <div class="leave-dates">
                    <div class="row text-center">
                        <div class="col-6">
                            <strong>من:</strong><br>
                            <small>{{ leave.start_date }}</small>
                        </div>
                        <div class="col-6">
                            <strong>إلى:</strong><br>
                            <small>{{ leave.end_date }}</small>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <span class="status-badge status-{{ leave.status }}">
                        {{ leave.get_status_display }}
                    </span>
                    
                    <div class="leave-actions">
                        <button class="btn btn-view btn-action">
                            <i class="fas fa-eye"></i>
                        </button>
                        {% if leave.status == 'pending' %}
                        <button class="btn btn-approve btn-action">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="btn btn-reject btn-action">
                            <i class="fas fa-times"></i>
                        </button>
                        {% endif %}
                    </div>
                </div>
                
                {% if leave.approved_by %}
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-user-check me-1"></i>وافق عليها: {{ leave.approved_by.get_full_name }}
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Leave pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.leave_type %}leave_type={{ request.GET.leave_type }}&{% endif %}{% if request.GET.employee %}employee={{ request.GET.employee }}&{% endif %}page={{ page_obj.previous_page_number }}">السابق</a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.leave_type %}leave_type={{ request.GET.leave_type }}&{% endif %}{% if request.GET.employee %}employee={{ request.GET.employee }}&{% endif %}page={{ num }}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.leave_type %}leave_type={{ request.GET.leave_type }}&{% endif %}{% if request.GET.employee %}employee={{ request.GET.employee }}&{% endif %}page={{ page_obj.next_page_number }}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}

    {% else %}
    <!-- Empty State -->
    <div class="empty-state">
        <i class="fas fa-calendar-times"></i>
        <h4>لا توجد إجازات</h4>
        <p>لم يتم العثور على أي طلبات إجازة بناءً على معايير البحث المحددة.</p>
        <button class="btn btn-add mt-3">
            <i class="fas fa-plus"></i> طلب إجازة جديد
        </button>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit form on filter change
document.querySelectorAll('select[name="status"], select[name="leave_type"], select[name="employee"]').forEach(function(select) {
    select.addEventListener('change', function() {
        this.form.submit();
    });
});

// Clear filters
function clearFilters() {
    window.location.href = '{% url "hr:leave_list" %}';
}
</script>
{% endblock %}
