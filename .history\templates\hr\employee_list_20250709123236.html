{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}قائمة الموظفين{% endblock %}

{% block extra_css %}
<style>
.employee-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.employee-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.employee-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-right: 15px;
}

.employee-info h5 {
    margin-bottom: 5px;
    color: #495057;
}

.employee-info p {
    margin-bottom: 3px;
    color: #6c757d;
    font-size: 0.9rem;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-active {
    background: #d1edff;
    color: #0c63e4;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.filter-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
}

.filter-card .form-control,
.filter-card .form-select {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    border-radius: 10px;
}

.filter-card .form-control::placeholder {
    color: rgba(255,255,255,0.7);
}

.filter-card .form-control:focus,
.filter-card .form-select:focus {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
}

.btn-filter {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    border-radius: 10px;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.btn-filter:hover {
    background: rgba(255,255,255,0.3);
    color: white;
    transform: translateY(-2px);
}

.btn-add {
    background: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-add:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(56, 239, 125, 0.4);
    color: white;
}

.employee-actions {
    display: flex;
    gap: 10px;
}

.btn-action {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.btn-view {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

.btn-view:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.stats-row {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
}

.stat-item {
    text-align: center;
    padding: 15px;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #dee2e6;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">👥 قائمة الموظفين</h1>
                    <p class="text-muted">إدارة وعرض جميع موظفي الوكالة</p>
                </div>
                <div>
                    <a href="{% url 'hr:dashboard' %}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
                    </a>
                    <a href="{% url 'hr:employee_add' %}" class="btn btn-add">
                        <i class="fas fa-plus"></i> إضافة موظف جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-row">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ employees|length }}</div>
                    <div class="stat-label">إجمالي الموظفين</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ departments|length }}</div>
                    <div class="stat-label">الأقسام</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ system_roles|length }}</div>
                    <div class="stat-label">الأدوار</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ page_obj.paginator.count }}</div>
                    <div class="stat-label">النتائج المعروضة</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="get" class="row align-items-end">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" placeholder="البحث بالاسم، البريد، أو رقم الموظف..." value="{{ request.GET.search }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">القسم</label>
                <select name="department" class="form-select">
                    <option value="">جميع الأقسام</option>
                    {% for dept in departments %}
                    <option value="{{ dept.name_ar }}" {% if request.GET.department == dept.name_ar %}selected{% endif %}>
                        {{ dept.name_ar }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">الدور</label>
                <select name="role" class="form-select">
                    <option value="">جميع الأدوار</option>
                    {% for role_key, role_name in system_roles %}
                    <option value="{{ role_key }}" {% if request.GET.role == role_key %}selected{% endif %}>
                        {{ role_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-filter w-100">
                    <i class="fas fa-search"></i> بحث
                </button>
            </div>
        </form>
    </div>

    <!-- Employee List -->
    {% if employees %}
    <div class="row">
        {% for employee in employees %}
        <div class="col-lg-6 col-xl-4">
            <div class="employee-card">
                <div class="d-flex align-items-center mb-3">
                    <div class="employee-avatar">
                        {{ employee.first_name|first }}{{ employee.last_name|first }}
                    </div>
                    <div class="employee-info flex-grow-1">
                        <h5>{{ employee.get_full_name }}</h5>
                        <p><i class="fas fa-briefcase me-1"></i>{{ employee.get_system_role_display }}</p>
                        <p><i class="fas fa-building me-1"></i>{{ employee.department|default:"غير محدد" }}</p>
                        <p><i class="fas fa-envelope me-1"></i>{{ employee.email }}</p>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between align-items-center">
                    <span class="status-badge status-{{ employee.is_active|yesno:'active,inactive' }}">
                        {{ employee.is_active|yesno:'نشط,غير نشط' }}
                    </span>
                    
                    <div class="employee-actions">
                        <a href="{% url 'hr:employee_detail' employee.pk %}" class="btn btn-view btn-action">
                            <i class="fas fa-eye"></i> عرض
                        </a>
                    </div>
                </div>
                
                {% if employee.employee_id %}
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-id-badge me-1"></i>رقم الموظف: {{ employee.employee_id }}
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Employee pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.department %}department={{ request.GET.department }}&{% endif %}{% if request.GET.role %}role={{ request.GET.role }}&{% endif %}page={{ page_obj.previous_page_number }}">السابق</a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.department %}department={{ request.GET.department }}&{% endif %}{% if request.GET.role %}role={{ request.GET.role }}&{% endif %}page={{ num }}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.department %}department={{ request.GET.department }}&{% endif %}{% if request.GET.role %}role={{ request.GET.role }}&{% endif %}page={{ page_obj.next_page_number }}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}

    {% else %}
    <!-- Empty State -->
    <div class="empty-state">
        <i class="fas fa-users"></i>
        <h4>لا توجد موظفين</h4>
        <p>لم يتم العثور على أي موظفين بناءً على معايير البحث المحددة.</p>
        <button class="btn btn-add mt-3">
            <i class="fas fa-plus"></i> إضافة موظف جديد
        </button>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit form on filter change
document.querySelectorAll('select[name="department"], select[name="role"]').forEach(function(select) {
    select.addEventListener('change', function() {
        this.form.submit();
    });
});

// Clear filters
function clearFilters() {
    window.location.href = '{% url "hr:employee_list" %}';
}
</script>
{% endblock %}
