{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة وجهات الباقة - {{ package.title_ar }}{% endblock %}

{% block extra_css %}
<style>
.manage-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.manage-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    text-align: center;
}

.package-info {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    border-left: 4px solid #17a2b8;
}

.destinations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.destination-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.destination-card:hover {
    border-color: #17a2b8;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.2);
}

.destination-card.selected {
    border-color: #17a2b8;
    background: #e3f2fd;
}

.destination-card .form-check {
    margin-bottom: 15px;
}

.destination-card .form-check-input {
    transform: scale(1.2);
    margin-top: 0.2em;
}

.destination-card .form-check-label {
    font-weight: 600;
    color: #495057;
    cursor: pointer;
}

.destination-info {
    margin-top: 10px;
}

.destination-info .info-item {
    margin-bottom: 8px;
    font-size: 0.9rem;
    color: #6c757d;
}

.destination-info .info-label {
    font-weight: 600;
    display: inline-block;
    width: 80px;
}

.btn-save {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
}

.btn-save:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
    margin-left: 15px;
}

.btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
    color: white;
}

.selection-summary {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.selection-count {
    font-weight: 600;
    color: #155724;
}

@media (max-width: 768px) {
    .destinations-grid {
        grid-template-columns: 1fr;
    }
    
    .manage-container {
        padding: 20px;
    }
    
    .btn-save, .btn-cancel {
        width: 100%;
        margin-bottom: 10px;
        margin-left: 0;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="manage-header">
        <h1 class="h3 mb-3">🗺️ إدارة وجهات الباقة</h1>
        <p class="mb-0">اختر الوجهات التي تشملها الباقة السياحية</p>
    </div>

    <div class="manage-container">
        <!-- Package Information -->
        <div class="package-info">
            <h5><i class="fas fa-suitcase"></i> معلومات الباقة</h5>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>العنوان:</strong> {{ package.title_ar }}</p>
                    <p><strong>الفئة:</strong> {{ package.category.name_ar }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>المدة:</strong> {{ package.duration_days }} أيام / {{ package.duration_nights }} ليالي</p>
                    <p><strong>السعر الأساسي:</strong> {{ package.base_price }} درهم</p>
                </div>
            </div>
        </div>

        <form method="post" id="destinationsForm">
            {% csrf_token %}
            
            <!-- Selection Summary -->
            <div class="selection-summary">
                <div class="selection-count">
                    <i class="fas fa-check-circle"></i>
                    تم اختيار <span id="selectedCount">{{ selected_destinations.count }}</span> وجهة من أصل {{ destinations.count }} وجهة متاحة
                </div>
            </div>

            <!-- Destinations Grid -->
            <div class="destinations-grid">
                {% for destination in destinations %}
                <div class="destination-card" data-destination-id="{{ destination.id }}">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="destinations" 
                               value="{{ destination.id }}" id="dest_{{ destination.id }}"
                               {% if destination in selected_destinations %}checked{% endif %}>
                        <label class="form-check-label" for="dest_{{ destination.id }}">
                            {{ destination.name_ar }}
                        </label>
                    </div>
                    
                    <div class="destination-info">
                        <div class="info-item">
                            <span class="info-label">الدولة:</span>
                            {{ destination.country.name_ar }}
                        </div>
                        {% if destination.city %}
                        <div class="info-item">
                            <span class="info-label">المدينة:</span>
                            {{ destination.city.name_ar }}
                        </div>
                        {% endif %}
                        <div class="info-item">
                            <span class="info-label">الشعبية:</span>
                            {{ destination.popularity_score }}%
                        </div>
                        <div class="info-item">
                            <span class="info-label">الحالة:</span>
                            {% if destination.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-map-marker-alt fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد وجهات متاحة</h4>
                        <p class="text-muted">يرجى إضافة وجهات سياحية أولاً</p>
                        <a href="{% url 'tours:destination_add' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إضافة وجهة جديدة
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Action Buttons -->
            <div class="text-center">
                <button type="submit" class="btn btn-save">
                    <i class="fas fa-save"></i> حفظ التغييرات
                </button>
                <a href="{% url 'tours:package_detail' package.pk %}" class="btn btn-cancel">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('input[name="destinations"]');
    const selectedCountSpan = document.getElementById('selectedCount');
    const destinationCards = document.querySelectorAll('.destination-card');
    
    // Update selection count
    function updateSelectionCount() {
        const checkedCount = document.querySelectorAll('input[name="destinations"]:checked').length;
        selectedCountSpan.textContent = checkedCount;
    }
    
    // Handle card clicks
    destinationCards.forEach(card => {
        const checkbox = card.querySelector('input[type="checkbox"]');
        
        // Update card appearance based on checkbox state
        function updateCardAppearance() {
            if (checkbox.checked) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }
        }
        
        // Initial state
        updateCardAppearance();
        
        // Card click handler
        card.addEventListener('click', function(e) {
            if (e.target.type !== 'checkbox' && e.target.tagName !== 'LABEL') {
                checkbox.checked = !checkbox.checked;
                updateCardAppearance();
                updateSelectionCount();
            }
        });
        
        // Checkbox change handler
        checkbox.addEventListener('change', function() {
            updateCardAppearance();
            updateSelectionCount();
        });
    });
    
    // Form submission
    const form = document.getElementById('destinationsForm');
    form.addEventListener('submit', function(e) {
        const checkedCount = document.querySelectorAll('input[name="destinations"]:checked').length;
        if (checkedCount === 0) {
            if (!confirm('لم تقم باختيار أي وجهة. هل تريد المتابعة؟')) {
                e.preventDefault();
                return false;
            }
        }
        console.log(`Saving ${checkedCount} destinations for package`);
    });
    
    console.log('Package destinations management loaded');
});
</script>
{% endblock %}
