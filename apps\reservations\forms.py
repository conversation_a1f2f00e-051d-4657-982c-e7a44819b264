"""
Forms for the reservations app.
"""
from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from .models import Reservation, ReservationParticipant, ReservationService
from apps.crm.models import Client
from apps.tours.models import TourPackage


class ReservationForm(forms.ModelForm):
    """Form for creating and updating reservations."""

    class Meta:
        model = Reservation
        fields = [
            'client', 'package', 'departure_date', 'return_date',
            'adults', 'children', 'infants',
            'special_requests', 'notes',
            'status', 'payment_status'
        ]
        widgets = {
            'client': forms.Select(attrs={'class': 'form-control'}),
            'package': forms.Select(attrs={'class': 'form-control'}),
            'departure_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'return_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'adults': forms.NumberInput(attrs={'class': 'form-control', 'min': 1}),
            'children': forms.NumberInput(attrs={'class': 'form-control', 'min': 0}),
            'infants': forms.NumberInput(attrs={'class': 'form-control', 'min': 0}),
            'special_requests': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'payment_status': forms.Select(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        # Get package from URL parameter if provided
        package_id = kwargs.pop('package_id', None)
        super().__init__(*args, **kwargs)

        # Set required fields
        self.fields['client'].required = True
        self.fields['package'].required = True
        self.fields['departure_date'].required = True
        self.fields['adults'].required = True

        # Set default values
        self.fields['adults'].initial = 1
        self.fields['children'].initial = 0
        self.fields['infants'].initial = 0
        self.fields['status'].initial = 'pending'
        self.fields['payment_status'].initial = 'pending'

        # Pre-select package if provided
        if package_id:
            try:
                package = TourPackage.objects.get(id=package_id)
                self.fields['package'].initial = package
                # Calculate return date based on package duration
                if self.fields['departure_date'].initial:
                    departure = self.fields['departure_date'].initial
                    if isinstance(departure, str):
                        departure = timezone.datetime.strptime(departure, '%Y-%m-%d').date()
                    self.fields['return_date'].initial = departure + timezone.timedelta(days=package.duration_days)
            except TourPackage.DoesNotExist:
                pass

    def clean_departure_date(self):
        """Validate departure date."""
        departure_date = self.cleaned_data.get('departure_date')
        if departure_date and departure_date < timezone.now().date():
            raise ValidationError('تاريخ المغادرة لا يمكن أن يكون في الماضي')
        return departure_date

    def clean_return_date(self):
        """Validate return date."""
        return_date = self.cleaned_data.get('return_date')
        departure_date = self.cleaned_data.get('departure_date')
        
        if return_date and departure_date and return_date <= departure_date:
            raise ValidationError('تاريخ العودة يجب أن يكون بعد تاريخ المغادرة')
        return return_date

    def clean_adults(self):
        """Validate number of adults."""
        adults = self.cleaned_data.get('adults')
        if adults and adults < 1:
            raise ValidationError('يجب أن يكون هناك بالغ واحد على الأقل')
        return adults

    def clean(self):
        """Custom validation for the form."""
        cleaned_data = super().clean()
        package = cleaned_data.get('package')
        adults = cleaned_data.get('adults', 0)
        children = cleaned_data.get('children', 0)
        infants = cleaned_data.get('infants', 0)

        total_participants = adults + children + infants

        # Check package capacity
        if package:
            if package.min_participants and total_participants < package.min_participants:
                raise ValidationError(f'العدد الأدنى للمشاركين هو {package.min_participants}')
            if package.max_participants and total_participants > package.max_participants:
                raise ValidationError(f'العدد الأقصى للمشاركين هو {package.max_participants}')

        return cleaned_data


class ReservationParticipantForm(forms.ModelForm):
    """Form for reservation participants."""

    class Meta:
        model = ReservationParticipant
        fields = [
            'participant_type', 'title', 'first_name', 'last_name',
            'date_of_birth', 'nationality', 'passport_number',
            'passport_expiry', 'special_needs'
        ]
        widgets = {
            'participant_type': forms.Select(attrs={'class': 'form-control'}),
            'title': forms.Select(attrs={'class': 'form-control'}),
            'first_name': forms.TextInput(attrs={'class': 'form-control'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control'}),
            'date_of_birth': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'nationality': forms.TextInput(attrs={'class': 'form-control'}),
            'passport_number': forms.TextInput(attrs={'class': 'form-control'}),
            'passport_expiry': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'special_needs': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set required fields
        self.fields['participant_type'].required = True
        self.fields['first_name'].required = True
        self.fields['last_name'].required = True
        self.fields['date_of_birth'].required = True

    def clean_passport_expiry(self):
        """Validate passport expiry date."""
        passport_expiry = self.cleaned_data.get('passport_expiry')
        if passport_expiry and passport_expiry < timezone.now().date():
            raise ValidationError('تاريخ انتهاء جواز السفر لا يمكن أن يكون في الماضي')
        return passport_expiry


class ReservationServiceForm(forms.ModelForm):
    """Form for additional reservation services."""

    class Meta:
        model = ReservationService
        fields = [
            'name', 'description', 'service_date',
            'price', 'quantity', 'is_included'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'service_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'min': 1}),
            'is_included': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set required fields
        self.fields['name'].required = True
        self.fields['service_date'].required = True
        self.fields['price'].required = True
        self.fields['quantity'].initial = 1


class ReservationSearchForm(forms.Form):
    """Form for searching reservations."""

    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث في الحجوزات...'
        })
    )
    
    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')] + Reservation.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    payment_status = forms.ChoiceField(
        choices=[('', 'جميع حالات الدفع')] + Reservation.PAYMENT_STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
