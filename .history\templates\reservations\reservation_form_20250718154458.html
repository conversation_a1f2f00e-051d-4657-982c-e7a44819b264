{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title|default:"إدارة الحجز" }}{% endblock %}

{% block extra_css %}
<style>
.form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.form-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    text-align: center;
}

.form-section {
    margin-bottom: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #28a745;
}

.form-section h5 {
    color: #495057;
    margin-bottom: 20px;
    font-weight: 600;
}

.form-section h5 i {
    margin-left: 10px;
    color: #28a745;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.required-field::after {
    content: " *";
    color: #dc3545;
}

.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.btn-submit {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
    margin-left: 15px;
}

.btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
    color: white;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
    display: block;
}

.help-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 5px;
}

.package-info {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    border-left: 4px solid #28a745;
}

.package-info h6 {
    color: #28a745;
    margin-bottom: 15px;
    font-weight: 600;
}

.participants-summary {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 152, 0, 0.1) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    border-left: 4px solid #ffc107;
}

.participants-summary h6 {
    color: #ff8f00;
    margin-bottom: 15px;
    font-weight: 600;
}

@media (max-width: 768px) {
    .form-container {
        padding: 20px;
    }
    
    .form-section {
        padding: 15px;
    }
    
    .btn-submit, .btn-cancel {
        width: 100%;
        margin-bottom: 10px;
        margin-left: 0;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="form-header">
        <h1 class="h3 mb-3">📅 {{ title }}</h1>
        <p class="mb-0">إدارة وتنظيم حجوزات العملاء</p>
    </div>

    <!-- Package Info (if selected) -->
    {% if selected_package %}
    <div class="package-info">
        <h6><i class="fas fa-suitcase"></i> الباقة المختارة</h6>
        <div class="row">
            <div class="col-md-6">
                <strong>{{ selected_package.title_ar }}</strong>
                <p class="mb-1">{{ selected_package.short_description_ar|truncatewords:20 }}</p>
            </div>
            <div class="col-md-3">
                <small class="text-muted">المدة:</small><br>
                <strong>{{ selected_package.duration_days }} أيام / {{ selected_package.duration_nights }} ليالي</strong>
            </div>
            <div class="col-md-3">
                <small class="text-muted">السعر الأساسي:</small><br>
                <strong>{{ selected_package.base_price }} درهم</strong>
            </div>
        </div>
    </div>
    {% endif %}

    <form method="post" enctype="multipart/form-data" id="reservationForm">
        {% csrf_token %}
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Client and Package Information -->
                <div class="form-section">
                    <h5><i class="fas fa-user"></i>معلومات العميل والباقة</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.client.label }}</label>
                                {{ form.client }}
                                {% if form.client.errors %}
                                    <span class="error-message">{{ form.client.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.package.label }}</label>
                                {{ form.package }}
                                {% if form.package.errors %}
                                    <span class="error-message">{{ form.package.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Travel Dates -->
                <div class="form-section">
                    <h5><i class="fas fa-calendar"></i>تواريخ السفر</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.departure_date.label }}</label>
                                {{ form.departure_date }}
                                {% if form.departure_date.errors %}
                                    <span class="error-message">{{ form.departure_date.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ form.return_date.label }}</label>
                                {{ form.return_date }}
                                {% if form.return_date.errors %}
                                    <span class="error-message">{{ form.return_date.errors.0 }}</span>
                                {% endif %}
                                <div class="help-text">سيتم حسابه تلقائياً بناءً على مدة الباقة</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Participants -->
                <div class="form-section">
                    <h5><i class="fas fa-users"></i>عدد المشاركين</h5>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.adults.label }}</label>
                                {{ form.adults }}
                                {% if form.adults.errors %}
                                    <span class="error-message">{{ form.adults.errors.0 }}</span>
                                {% endif %}
                                <div class="help-text">من 12 سنة فما فوق</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">{{ form.children.label }}</label>
                                {{ form.children }}
                                {% if form.children.errors %}
                                    <span class="error-message">{{ form.children.errors.0 }}</span>
                                {% endif %}
                                <div class="help-text">من 2 إلى 11 سنة</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">{{ form.infants.label }}</label>
                                {{ form.infants }}
                                {% if form.infants.errors %}
                                    <span class="error-message">{{ form.infants.errors.0 }}</span>
                                {% endif %}
                                <div class="help-text">أقل من سنتين</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="form-section">
                    <h5><i class="fas fa-clipboard"></i>معلومات إضافية</h5>
                    
                    <div class="form-group">
                        <label class="form-label">{{ form.special_requests.label }}</label>
                        {{ form.special_requests }}
                        {% if form.special_requests.errors %}
                            <span class="error-message">{{ form.special_requests.errors.0 }}</span>
                        {% endif %}
                        <div class="help-text">طلبات خاصة أو احتياجات معينة</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">{{ form.notes.label }}</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <span class="error-message">{{ form.notes.errors.0 }}</span>
                        {% endif %}
                        <div class="help-text">ملاحظات داخلية</div>
                    </div>
                </div>

                <!-- Status -->
                <div class="form-section">
                    <h5><i class="fas fa-flag"></i>حالة الحجز</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ form.status.label }}</label>
                                {{ form.status }}
                                {% if form.status.errors %}
                                    <span class="error-message">{{ form.status.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ form.payment_status.label }}</label>
                                {{ form.payment_status }}
                                {% if form.payment_status.errors %}
                                    <span class="error-message">{{ form.payment_status.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="text-center">
                    <button type="submit" class="btn btn-submit">
                        <i class="fas fa-save"></i> {{ submit_text|default:"حفظ الحجز" }}
                    </button>
                    <a href="{% url 'reservations:reservation_list' %}" class="btn btn-cancel">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Participants Summary -->
                <div class="participants-summary">
                    <h6><i class="fas fa-calculator"></i> ملخص المشاركين</h6>
                    <div id="participantsSummary">
                        <div class="d-flex justify-content-between">
                            <span>البالغون:</span>
                            <span id="adultsCount">1</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>الأطفال:</span>
                            <span id="childrenCount">0</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>الرضع:</span>
                            <span id="infantsCount">0</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between">
                            <strong>المجموع:</strong>
                            <strong id="totalCount">1</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const adultsField = document.getElementById('{{ form.adults.id_for_label }}');
    const childrenField = document.getElementById('{{ form.children.id_for_label }}');
    const infantsField = document.getElementById('{{ form.infants.id_for_label }}');
    
    function updateParticipantsSummary() {
        const adults = parseInt(adultsField.value) || 0;
        const children = parseInt(childrenField.value) || 0;
        const infants = parseInt(infantsField.value) || 0;
        const total = adults + children + infants;
        
        document.getElementById('adultsCount').textContent = adults;
        document.getElementById('childrenCount').textContent = children;
        document.getElementById('infantsCount').textContent = infants;
        document.getElementById('totalCount').textContent = total;
    }
    
    // Update summary when values change
    [adultsField, childrenField, infantsField].forEach(field => {
        if (field) {
            field.addEventListener('input', updateParticipantsSummary);
            field.addEventListener('change', updateParticipantsSummary);
        }
    });
    
    // Initial update
    updateParticipantsSummary();
    
    // Form validation
    const form = document.getElementById('reservationForm');
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Check if at least one adult
        if (parseInt(adultsField.value) < 1) {
            isValid = false;
            alert('يجب أن يكون هناك بالغ واحد على الأقل');
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
