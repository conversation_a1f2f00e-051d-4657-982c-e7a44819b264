{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
.form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    text-align: center;
}

.form-section {
    margin-bottom: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.form-section h5 {
    color: #495057;
    margin-bottom: 20px;
    font-weight: 600;
}

.form-section h5 i {
    margin-left: 10px;
    color: #667eea;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.required-field::after {
    content: " *";
    color: #dc3545;
}

.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-check-input {
    width: 1.2em;
    height: 1.2em;
    margin-top: 0.25em;
    border: 2px solid #667eea;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.form-check-label {
    margin-right: 10px;
    font-weight: 500;
    color: #495057;
}

.btn-submit {
    background: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(56, 239, 125, 0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
    margin-left: 15px;
}

.btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
    color: white;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
    display: block;
}

.help-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 5px;
}

.form-tips {
    background: linear-gradient(135deg, rgba(56, 239, 125, 0.1) 0%, rgba(17, 153, 142, 0.1) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    border-left: 4px solid #38ef7d;
}

.form-tips h6 {
    color: #11998e;
    margin-bottom: 15px;
    font-weight: 600;
}

.form-tips ul {
    margin-bottom: 0;
    padding-right: 20px;
}

.form-tips li {
    color: #495057;
    margin-bottom: 5px;
}

.contract-preview {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    border-left: 4px solid #667eea;
}

.contract-preview h6 {
    color: #667eea;
    margin-bottom: 15px;
    font-weight: 600;
}

.preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(102, 126, 234, 0.2);
}

.preview-item:last-child {
    border-bottom: none;
}

.preview-label {
    color: #6c757d;
    font-weight: 500;
}

.preview-value {
    color: #495057;
    font-weight: 600;
}

.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.file-upload-area.dragover {
    border-color: #38ef7d;
    background: rgba(56, 239, 125, 0.1);
}

.file-upload-icon {
    font-size: 3rem;
    color: #dee2e6;
    margin-bottom: 15px;
}

.file-upload-text {
    color: #6c757d;
    margin-bottom: 10px;
}

.file-upload-hint {
    color: #adb5bd;
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .form-container {
        padding: 20px;
    }
    
    .form-section {
        padding: 15px;
    }
    
    .btn-submit, .btn-cancel {
        width: 100%;
        margin-bottom: 10px;
        margin-left: 0;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="form-header">
        <h1 class="h3 mb-3">📄 {{ title }}</h1>
        <p class="mb-0">إنشاء وإدارة عقود الموردين والخدمات</p>
    </div>

    <!-- Form Tips -->
    <div class="form-tips">
        <h6><i class="fas fa-lightbulb"></i> نصائح مهمة</h6>
        <ul>
            <li>تأكد من صحة تواريخ العقد قبل الحفظ</li>
            <li>قيمة العقد اختيارية ويمكن تركها فارغة</li>
            <li>يمكن رفع ملف العقد بصيغة PDF أو Word</li>
            <li>حالة العقد الافتراضية هي "مسودة"</li>
            <li>التجديد التلقائي يعني تجديد العقد تلقائياً عند انتهائه</li>
        </ul>
    </div>

    <form method="post" enctype="multipart/form-data" id="contractForm">
        {% csrf_token %}
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="form-section">
                    <h5><i class="fas fa-info-circle"></i>المعلومات الأساسية</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.supplier.label }}</label>
                                {{ form.supplier }}
                                {% if form.supplier.errors %}
                                    <span class="error-message">{{ form.supplier.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.contract_type.label }}</label>
                                {{ form.contract_type }}
                                {% if form.contract_type.errors %}
                                    <span class="error-message">{{ form.contract_type.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label required-field">{{ form.title.label }}</label>
                        {{ form.title }}
                        {% if form.title.errors %}
                            <span class="error-message">{{ form.title.errors.0 }}</span>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">{{ form.description.label }}</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <span class="error-message">{{ form.description.errors.0 }}</span>
                        {% endif %}
                        <div class="help-text">وصف تفصيلي للعقد والخدمات المقدمة</div>
                    </div>
                </div>

                <!-- Dates -->
                <div class="form-section">
                    <h5><i class="fas fa-calendar"></i>التواريخ</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.start_date.label }}</label>
                                {{ form.start_date }}
                                {% if form.start_date.errors %}
                                    <span class="error-message">{{ form.start_date.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label required-field">{{ form.end_date.label }}</label>
                                {{ form.end_date }}
                                {% if form.end_date.errors %}
                                    <span class="error-message">{{ form.end_date.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ form.renewal_date.label }}</label>
                                {{ form.renewal_date }}
                                {% if form.renewal_date.errors %}
                                    <span class="error-message">{{ form.renewal_date.errors.0 }}</span>
                                {% endif %}
                                <div class="help-text">تاريخ التجديد (اختياري)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ form.status.label }}</label>
                                {{ form.status }}
                                {% if form.status.errors %}
                                    <span class="error-message">{{ form.status.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Financial Terms -->
                <div class="form-section">
                    <h5><i class="fas fa-money-bill"></i>الشروط المالية</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ form.contract_value.label }}</label>
                                {{ form.contract_value }}
                                {% if form.contract_value.errors %}
                                    <span class="error-message">{{ form.contract_value.errors.0 }}</span>
                                {% endif %}
                                <div class="help-text">القيمة الإجمالية للعقد بالدرهم المغربي</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check">
                                    {{ form.auto_renewal }}
                                    <label class="form-check-label" for="{{ form.auto_renewal.id_for_label }}">
                                        {{ form.auto_renewal.label }}
                                    </label>
                                </div>
                                {% if form.auto_renewal.errors %}
                                    <span class="error-message">{{ form.auto_renewal.errors.0 }}</span>
                                {% endif %}
                                <div class="help-text">تجديد العقد تلقائياً عند انتهائه</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">{{ form.payment_terms.label }}</label>
                        {{ form.payment_terms }}
                        {% if form.payment_terms.errors %}
                            <span class="error-message">{{ form.payment_terms.errors.0 }}</span>
                        {% endif %}
                        <div class="help-text">شروط الدفع والاستحقاق</div>
                    </div>
                </div>

                <!-- Contract File -->
                <div class="form-section">
                    <h5><i class="fas fa-file-upload"></i>ملف العقد</h5>
                    
                    <div class="form-group">
                        <label class="form-label">{{ form.contract_file.label }}</label>
                        <div class="file-upload-area" onclick="document.getElementById('{{ form.contract_file.id_for_label }}').click()">
                            <div class="file-upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="file-upload-text">انقر لاختيار ملف العقد</div>
                            <div class="file-upload-hint">PDF, DOC, DOCX (الحد الأقصى: 10MB)</div>
                        </div>
                        {{ form.contract_file }}
                        {% if form.contract_file.errors %}
                            <span class="error-message">{{ form.contract_file.errors.0 }}</span>
                        {% endif %}
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="text-center">
                    <button type="submit" class="btn btn-submit">
                        <i class="fas fa-save"></i> {{ submit_text|default:"حفظ العقد" }}
                    </button>
                    <a href="{% url 'suppliers:contract_list' %}" class="btn btn-cancel">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Contract Preview -->
                <div class="contract-preview">
                    <h6><i class="fas fa-eye"></i> معاينة العقد</h6>
                    <div id="contractPreview">
                        <div class="preview-item">
                            <span class="preview-label">المورد:</span>
                            <span class="preview-value" id="previewSupplier">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">العنوان:</span>
                            <span class="preview-value" id="previewTitle">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">النوع:</span>
                            <span class="preview-value" id="previewType">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">تاريخ البداية:</span>
                            <span class="preview-value" id="previewStartDate">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">تاريخ النهاية:</span>
                            <span class="preview-value" id="previewEndDate">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">القيمة:</span>
                            <span class="preview-value" id="previewValue">غير محدد</span>
                        </div>
                        <div class="preview-item">
                            <span class="preview-label">الحالة:</span>
                            <span class="preview-value" id="previewStatus">مسودة</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form validation and preview
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('contractForm');
    const supplierField = document.getElementById('{{ form.supplier.id_for_label }}');
    const titleField = document.getElementById('{{ form.title.id_for_label }}');
    const typeField = document.getElementById('{{ form.contract_type.id_for_label }}');
    const startDateField = document.getElementById('{{ form.start_date.id_for_label }}');
    const endDateField = document.getElementById('{{ form.end_date.id_for_label }}');
    const valueField = document.getElementById('{{ form.contract_value.id_for_label }}');
    const statusField = document.getElementById('{{ form.status.id_for_label }}');
    
    // Update preview
    function updatePreview() {
        document.getElementById('previewSupplier').textContent = 
            supplierField.options[supplierField.selectedIndex]?.text || 'غير محدد';
        document.getElementById('previewTitle').textContent = 
            titleField.value || 'غير محدد';
        document.getElementById('previewType').textContent = 
            typeField.options[typeField.selectedIndex]?.text || 'غير محدد';
        document.getElementById('previewStartDate').textContent = 
            startDateField.value || 'غير محدد';
        document.getElementById('previewEndDate').textContent = 
            endDateField.value || 'غير محدد';
        document.getElementById('previewValue').textContent = 
            valueField.value ? valueField.value + ' درهم' : 'غير محدد';
        document.getElementById('previewStatus').textContent = 
            statusField.options[statusField.selectedIndex]?.text || 'مسودة';
    }
    
    // Add event listeners
    [supplierField, titleField, typeField, startDateField, endDateField, valueField, statusField].forEach(field => {
        if (field) {
            field.addEventListener('change', updatePreview);
            field.addEventListener('input', updatePreview);
        }
    });
    
    // Initial preview update
    updatePreview();
    
    // File upload handling
    const fileInput = document.getElementById('{{ form.contract_file.id_for_label }}');
    const uploadArea = document.querySelector('.file-upload-area');
    
    if (fileInput && uploadArea) {
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                uploadArea.querySelector('.file-upload-text').textContent = 
                    'تم اختيار: ' + this.files[0].name;
            }
        });
        
        // Drag and drop
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function() {
            this.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            fileInput.files = e.dataTransfer.files;
            if (fileInput.files.length > 0) {
                this.querySelector('.file-upload-text').textContent = 
                    'تم اختيار: ' + fileInput.files[0].name;
            }
        });
    }
    
    // Form validation
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        const requiredFields = [supplierField, titleField, typeField, startDateField, endDateField];
        requiredFields.forEach(field => {
            if (field && !field.value.trim()) {
                isValid = false;
                field.style.borderColor = '#dc3545';
            } else if (field) {
                field.style.borderColor = '#e9ecef';
            }
        });
        
        // Check date validation
        if (startDateField.value && endDateField.value) {
            if (new Date(startDateField.value) >= new Date(endDateField.value)) {
                isValid = false;
                endDateField.style.borderColor = '#dc3545';
                alert('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
            }
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
