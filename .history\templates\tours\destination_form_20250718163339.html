{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if object %}تعديل الوجهة{% else %}إضافة وجهة جديدة{% endif %}
{% endblock %}

{% block extra_css %}
<style>
.form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.form-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    text-align: center;
}

.form-section {
    margin-bottom: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #17a2b8;
}

.form-section h5 {
    color: #495057;
    margin-bottom: 20px;
    font-weight: 600;
}

.form-section h5 i {
    margin-left: 10px;
    color: #17a2b8;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.required-field::after {
    content: " *";
    color: #dc3545;
}

.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #17a2b8;
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

.btn-submit {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
    margin-left: 15px;
}

.btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
    color: white;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
    display: block;
}

.help-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 5px;
}

.language-tabs {
    border-bottom: 2px solid #17a2b8;
    margin-bottom: 20px;
}

.language-tab {
    background: none;
    border: none;
    padding: 10px 20px;
    color: #6c757d;
    font-weight: 600;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.language-tab.active {
    color: #17a2b8;
    border-bottom-color: #17a2b8;
}

.language-content {
    display: none;
}

.language-content.active {
    display: block;
}

@media (max-width: 768px) {
    .form-container {
        padding: 20px;
    }
    
    .form-section {
        padding: 15px;
    }
    
    .btn-submit, .btn-cancel {
        width: 100%;
        margin-bottom: 10px;
        margin-left: 0;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="form-header">
        <h1 class="h3 mb-3">
            🌍 {% if object %}تعديل الوجهة{% else %}إضافة وجهة جديدة{% endif %}
        </h1>
        <p class="mb-0">إدارة الوجهات السياحية والمعالم</p>
    </div>

    <form method="post" enctype="multipart/form-data" id="destinationForm">
        {% csrf_token %}
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="form-section">
                    <h5><i class="fas fa-info-circle"></i>المعلومات الأساسية</h5>
                    
                    <!-- Language Tabs -->
                    <div class="language-tabs">
                        <button type="button" class="language-tab active" data-lang="ar">العربية</button>
                        <button type="button" class="language-tab" data-lang="fr">Français</button>
                        <button type="button" class="language-tab" data-lang="en">English</button>
                    </div>
                    
                    <!-- Arabic Content -->
                    <div class="language-content active" id="content-ar">
                        <div class="form-group">
                            <label class="form-label required-field" for="id_name_ar">الاسم بالعربية</label>
                            <input type="text" name="name_ar" class="form-control" id="id_name_ar" required>
                            <div class="error-message" id="error_name_ar"></div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="id_description_ar">الوصف بالعربية</label>
                            <textarea name="description_ar" class="form-control" id="id_description_ar" rows="4"></textarea>
                            <div class="help-text">وصف مفصل للوجهة باللغة العربية</div>
                            <div class="error-message" id="error_description_ar"></div>
                        </div>
                    </div>
                    
                    <!-- French Content -->
                    <div class="language-content" id="content-fr">
                        <div class="form-group">
                            <label class="form-label required-field" for="id_name_fr">Nom en français</label>
                            <input type="text" name="name_fr" class="form-control" id="id_name_fr" required>
                            <div class="error-message" id="error_name_fr"></div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="id_description_fr">Description en français</label>
                            <textarea name="description_fr" class="form-control" id="id_description_fr" rows="4"></textarea>
                            <div class="help-text">Description détaillée de la destination en français</div>
                            <div class="error-message" id="error_description_fr"></div>
                        </div>
                    </div>
                    
                    <!-- English Content -->
                    <div class="language-content" id="content-en">
                        <div class="form-group">
                            <label class="form-label required-field" for="id_name_en">Name in English</label>
                            <input type="text" name="name_en" class="form-control" id="id_name_en" required>
                            <div class="error-message" id="error_name_en"></div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="id_description_en">Description in English</label>
                            <textarea name="description_en" class="form-control" id="id_description_en" rows="4"></textarea>
                            <div class="help-text">Detailed description of the destination in English</div>
                            <div class="error-message" id="error_description_en"></div>
                        </div>
                    </div>
                </div>

                <!-- Location Information -->
                <div class="form-section">
                    <h5><i class="fas fa-map-marker-alt"></i>معلومات الموقع</h5>

                    <div class="form-group">
                        <label class="form-label required-field" for="id_country">الدولة</label>
                        <select name="country" class="form-control" id="id_country" required>
                            <option value="">-- اختر الدولة --</option>
                            {% for country in countries %}
                                <option value="{{ country.id }}">{{ country.name_ar }}</option>
                            {% endfor %}
                        </select>
                        <div class="error-message" id="error_country"></div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="text-center">
                    <button type="submit" class="btn btn-submit">
                        <i class="fas fa-save"></i> 
                        {% if object %}حفظ التغييرات{% else %}إضافة الوجهة{% endif %}
                    </button>
                    <a href="{% url 'tours:destination_list' %}" class="btn btn-cancel">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Help Information -->
                <div class="form-section">
                    <h5><i class="fas fa-question-circle"></i>معلومات مساعدة</h5>
                    <div class="help-text">
                        <p><strong>نصائح لإضافة وجهة:</strong></p>
                        <ul>
                            <li>أدخل اسم الوجهة بالعربية والفرنسية والإنجليزية</li>
                            <li>اكتب وصفاً جذاباً للوجهة</li>
                            <li>اختر الدولة المناسبة</li>
                            <li>تأكد من صحة المعلومات قبل الحفظ</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Language tab switching
    const languageTabs = document.querySelectorAll('.language-tab');
    const languageContents = document.querySelectorAll('.language-content');

    languageTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetLang = this.getAttribute('data-lang');

            // Remove active class from all tabs and contents
            languageTabs.forEach(t => t.classList.remove('active'));
            languageContents.forEach(c => c.classList.remove('active'));

            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            document.getElementById(`content-${targetLang}`).classList.add('active');
        });
    });

    // Populate form fields if editing
    {% if object %}
    document.getElementById('id_name_ar').value = '{{ object.name_ar|escapejs }}';
    document.getElementById('id_name_fr').value = '{{ object.name_fr|escapejs }}';
    document.getElementById('id_name_en').value = '{{ object.name_en|escapejs }}';
    document.getElementById('id_description_ar').value = '{{ object.description_ar|escapejs }}';
    document.getElementById('id_description_fr').value = '{{ object.description_fr|escapejs }}';
    document.getElementById('id_description_en').value = '{{ object.description_en|escapejs }}';
    document.getElementById('id_country').value = '{{ object.country.id }}';
    {% endif %}
    
    // Simple form submission without complex validation
    const form = document.getElementById('destinationForm');
    form.addEventListener('submit', function(e) {
        console.log('Form submitted');
        // Let the form submit naturally
        return true;
    });
    
    console.log('Destination form loaded');
});
</script>
{% endblock %}
