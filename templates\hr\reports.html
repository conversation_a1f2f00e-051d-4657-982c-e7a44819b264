{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}تقارير الموارد البشرية{% endblock %}

{% block extra_css %}
<style>
.report-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.report-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.report-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    text-align: center;
}

.stat-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.2);
}

.stat-card.employees {
    border-left-color: #38ef7d;
}

.stat-card.leaves {
    border-left-color: #fdbb2d;
}

.stat-card.attendance {
    border-left-color: #667eea;
}

.stat-card.payroll {
    border-left-color: #ff6b6b;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 10px;
    display: block;
}

.stat-card.employees .stat-number {
    color: #38ef7d;
}

.stat-card.leaves .stat-number {
    color: #fdbb2d;
}

.stat-card.attendance .stat-number {
    color: #667eea;
}

.stat-card.payroll .stat-number {
    color: #ff6b6b;
}

.stat-label {
    color: #6c757d;
    font-size: 1rem;
    font-weight: 500;
}

.chart-container {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.chart-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.chart-title i {
    margin-left: 10px;
    color: #667eea;
}

.department-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.department-name {
    font-weight: 500;
    color: #495057;
}

.department-count {
    background: #667eea;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: 500;
}

.leave-type-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #fdbb2d;
}

.leave-type-name {
    color: #495057;
}

.leave-type-count {
    background: #fdbb2d;
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

.progress-bar-custom {
    height: 8px;
    border-radius: 10px;
    background: #e9ecef;
    overflow: hidden;
    margin-top: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.btn-export {
    background: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
    margin-right: 10px;
}

.btn-export:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(56, 239, 125, 0.4);
    color: white;
}

.btn-print {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-print:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.metric-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.metric-row:last-child {
    border-bottom: none;
}

.metric-label {
    color: #6c757d;
    font-weight: 500;
}

.metric-value {
    color: #495057;
    font-weight: 600;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-left: 15px;
    color: #667eea;
}

@media print {
    .btn-export, .btn-print {
        display: none;
    }
    
    .report-card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="report-header">
        <h1 class="h2 mb-3">📊 تقارير الموارد البشرية</h1>
        <p class="mb-4">تقرير شامل عن حالة الموارد البشرية في الوكالة</p>
        <div>
            <button class="btn btn-export">
                <i class="fas fa-download"></i> تصدير PDF
            </button>
            <button class="btn btn-print" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة
            </button>
            <a href="{% url 'hr:dashboard' %}" class="btn btn-outline-light">
                <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>

    <!-- Key Statistics -->
    <div class="stat-grid">
        <div class="stat-card employees">
            <span class="stat-number">{{ employee_stats.total_employees }}</span>
            <div class="stat-label">إجمالي الموظفين</div>
        </div>
        <div class="stat-card leaves">
            <span class="stat-number">{{ leave_stats.pending_leaves }}</span>
            <div class="stat-label">إجازات معلقة</div>
        </div>
        <div class="stat-card attendance">
            <span class="stat-number">{{ attendance_stats.monthly_attendance_rate }}%</span>
            <div class="stat-label">معدل الحضور الشهري</div>
        </div>
        <div class="stat-card payroll">
            <span class="stat-number">{{ payroll_stats.total_payroll|floatformat:0 }}</span>
            <div class="stat-label">إجمالي الرواتب (درهم)</div>
        </div>
    </div>

    <div class="row">
        <!-- Employee Statistics -->
        <div class="col-lg-6">
            <div class="report-card">
                <h3 class="section-title">
                    <i class="fas fa-users"></i>
                    إحصائيات الموظفين
                </h3>
                
                <div class="metric-row">
                    <span class="metric-label">إجمالي الموظفين النشطين</span>
                    <span class="metric-value">{{ employee_stats.total_employees }}</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">الموظفين الجدد هذا الشهر</span>
                    <span class="metric-value">{{ employee_stats.new_employees }}</span>
                </div>
                
                <h5 class="mt-4 mb-3">توزيع الموظفين حسب القسم</h5>
                {% for dept in employee_stats.employees_by_department %}
                <div class="department-item">
                    <span class="department-name">{{ dept.name_ar }}</span>
                    <span class="department-count">{{ dept.employee_count }} موظف</span>
                </div>
                {% empty %}
                <p class="text-muted text-center">لا توجد أقسام</p>
                {% endfor %}
            </div>
        </div>

        <!-- Leave Statistics -->
        <div class="col-lg-6">
            <div class="report-card">
                <h3 class="section-title">
                    <i class="fas fa-calendar-times"></i>
                    إحصائيات الإجازات
                </h3>
                
                <div class="metric-row">
                    <span class="metric-label">إجمالي طلبات الإجازة</span>
                    <span class="metric-value">{{ leave_stats.total_leaves }}</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">في الانتظار</span>
                    <span class="metric-value">{{ leave_stats.pending_leaves }}</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">موافق عليها</span>
                    <span class="metric-value">{{ leave_stats.approved_leaves }}</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">إجازات هذا الشهر</span>
                    <span class="metric-value">{{ leave_stats.monthly_leaves }}</span>
                </div>
                
                <h5 class="mt-4 mb-3">الإجازات حسب النوع</h5>
                {% for leave_type in leave_stats.leave_by_type %}
                <div class="leave-type-item">
                    <span class="leave-type-name">{{ leave_type.leave_type }}</span>
                    <span class="leave-type-count">{{ leave_type.count }}</span>
                </div>
                {% empty %}
                <p class="text-muted text-center">لا توجد إجازات</p>
                {% endfor %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Attendance Statistics -->
        <div class="col-lg-6">
            <div class="report-card">
                <h3 class="section-title">
                    <i class="fas fa-clock"></i>
                    إحصائيات الحضور
                </h3>
                
                <div class="metric-row">
                    <span class="metric-label">إجمالي سجلات الحضور</span>
                    <span class="metric-value">{{ attendance_stats.total_attendance_records }}</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">معدل الحضور الشهري</span>
                    <span class="metric-value">{{ attendance_stats.monthly_attendance_rate }}%</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">متوسط ساعات العمل</span>
                    <span class="metric-value">{{ attendance_stats.average_working_hours|floatformat:1 }} ساعة</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">إجمالي ساعات الإضافي</span>
                    <span class="metric-value">{{ attendance_stats.total_overtime_hours|floatformat:1 }} ساعة</span>
                </div>
                
                <!-- Attendance Rate Progress Bar -->
                <div class="mt-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span>معدل الحضور</span>
                        <span>{{ attendance_stats.monthly_attendance_rate }}%</span>
                    </div>
                    <div class="progress-bar-custom">
                        <div class="progress-fill" style="width: {{ attendance_stats.monthly_attendance_rate }}%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payroll Statistics -->
        <div class="col-lg-6">
            <div class="report-card">
                <h3 class="section-title">
                    <i class="fas fa-money-bill-wave"></i>
                    إحصائيات الرواتب
                </h3>
                
                <div class="metric-row">
                    <span class="metric-label">إجمالي الرواتب المدفوعة</span>
                    <span class="metric-value">{{ payroll_stats.total_payroll|floatformat:0 }} درهم</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">متوسط الراتب</span>
                    <span class="metric-value">{{ payroll_stats.average_salary|floatformat:0 }} درهم</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">إجمالي بدل الإضافي</span>
                    <span class="metric-value">{{ payroll_stats.total_overtime_pay|floatformat:0 }} درهم</span>
                </div>
                
                <!-- Payroll Breakdown Chart Placeholder -->
                <div class="mt-4">
                    <canvas id="payrollChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Footer -->
    <div class="report-card">
        <div class="row text-center">
            <div class="col-md-3">
                <h4 class="text-primary">{{ employee_stats.total_employees }}</h4>
                <p class="text-muted">موظف نشط</p>
            </div>
            <div class="col-md-3">
                <h4 class="text-warning">{{ leave_stats.pending_leaves }}</h4>
                <p class="text-muted">طلب إجازة معلق</p>
            </div>
            <div class="col-md-3">
                <h4 class="text-info">{{ attendance_stats.monthly_attendance_rate }}%</h4>
                <p class="text-muted">معدل الحضور</p>
            </div>
            <div class="col-md-3">
                <h4 class="text-success">{{ payroll_stats.total_payroll|floatformat:0 }}</h4>
                <p class="text-muted">درهم (إجمالي الرواتب)</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Payroll Chart
const payrollCtx = document.getElementById('payrollChart').getContext('2d');
const payrollChart = new Chart(payrollCtx, {
    type: 'doughnut',
    data: {
        labels: ['الراتب الأساسي', 'بدل الإضافي', 'البدلات الأخرى'],
        datasets: [{
            data: [
                {{ payroll_stats.total_payroll|default:0 }},
                {{ payroll_stats.total_overtime_pay|default:0 }},
                {{ payroll_stats.total_payroll|default:0 }} * 0.1
            ],
            backgroundColor: [
                '#667eea',
                '#38ef7d',
                '#fdbb2d'
            ],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true
                }
            }
        }
    }
});

// Print functionality
function printReport() {
    window.print();
}

// Export functionality (placeholder)
function exportToPDF() {
    alert('سيتم تنفيذ وظيفة التصدير قريباً');
}
</script>
{% endblock %}
