{% extends 'base.html' %}
{% load static %}

{% block title %}{{ destination.name_ar }}{% endblock %}

{% block extra_css %}
<style>
.destination-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 40px;
    margin-bottom: 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.destination-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
    z-index: 1;
}

.destination-header-content {
    position: relative;
    z-index: 2;
}

.destination-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 15px;
    margin-bottom: 20px;
}

.destination-placeholder {
    width: 100%;
    height: 300px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 4rem;
    margin-bottom: 20px;
}

.info-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 25px;
    border-left: 4px solid #667eea;
}

.info-card h5 {
    color: #495057;
    margin-bottom: 20px;
    font-weight: 600;
}

.info-card h5 i {
    margin-left: 10px;
    color: #667eea;
}

.language-tabs {
    border-bottom: 2px solid #667eea;
    margin-bottom: 20px;
}

.language-tab {
    background: none;
    border: none;
    padding: 10px 20px;
    color: #6c757d;
    font-weight: 600;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.language-tab.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.language-content {
    display: none;
}

.language-content.active {
    display: block;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 15px;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    display: block;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 5px;
}

.action-buttons {
    position: sticky;
    top: 20px;
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn-action {
    width: 100%;
    margin-bottom: 10px;
    border-radius: 25px;
    padding: 12px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-action:hover {
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .destination-header {
        padding: 20px;
    }
    
    .info-card {
        padding: 15px;
    }
    
    .action-buttons {
        position: static;
        margin-top: 20px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Destination Header -->
    <div class="destination-header">
        <div class="destination-header-content">
            <h1 class="display-4 mb-3">🌍 {{ destination.name_ar }}</h1>
            <p class="lead mb-0">{{ destination.country.name_ar }}</p>
            {% if destination.city %}
                <p class="mb-0">{{ destination.city.name_ar }}</p>
            {% endif %}
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Main Image -->
            <div class="info-card">
                {% if destination.main_image %}
                    <img src="{{ destination.main_image.url }}" alt="{{ destination.name_ar }}" class="destination-image">
                {% else %}
                    <div class="destination-placeholder">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                {% endif %}
            </div>

            <!-- Description -->
            <div class="info-card">
                <h5><i class="fas fa-info-circle"></i>الوصف</h5>
                
                <!-- Language Tabs -->
                <div class="language-tabs">
                    <button type="button" class="language-tab active" data-lang="ar">العربية</button>
                    <button type="button" class="language-tab" data-lang="fr">Français</button>
                    <button type="button" class="language-tab" data-lang="en">English</button>
                </div>
                
                <!-- Arabic Content -->
                <div class="language-content active" id="content-ar">
                    <h6>{{ destination.name_ar }}</h6>
                    {% if destination.description_ar %}
                        <p>{{ destination.description_ar|linebreaks }}</p>
                    {% else %}
                        <p class="text-muted">لا يوجد وصف متاح باللغة العربية</p>
                    {% endif %}
                </div>
                
                <!-- French Content -->
                <div class="language-content" id="content-fr">
                    <h6>{{ destination.name_fr }}</h6>
                    {% if destination.description_fr %}
                        <p>{{ destination.description_fr|linebreaks }}</p>
                    {% else %}
                        <p class="text-muted">Aucune description disponible en français</p>
                    {% endif %}
                </div>
                
                <!-- English Content -->
                <div class="language-content" id="content-en">
                    <h6>{{ destination.name_en }}</h6>
                    {% if destination.description_en %}
                        <p>{{ destination.description_en|linebreaks }}</p>
                    {% else %}
                        <p class="text-muted">No description available in English</p>
                    {% endif %}
                </div>
            </div>

            <!-- Location Information -->
            <div class="info-card">
                <h5><i class="fas fa-map-marker-alt"></i>معلومات الموقع</h5>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>الدولة:</strong> {{ destination.country.name_ar }}</p>
                        {% if destination.city %}
                            <p><strong>المدينة:</strong> {{ destination.city.name_ar }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {% if destination.latitude and destination.longitude %}
                            <p><strong>خط العرض:</strong> {{ destination.latitude }}</p>
                            <p><strong>خط الطول:</strong> {{ destination.longitude }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            {% if destination.climate or destination.best_time_to_visit or destination.activities %}
            <div class="info-card">
                <h5><i class="fas fa-info"></i>معلومات إضافية</h5>
                <div class="row">
                    {% if destination.climate %}
                    <div class="col-md-4">
                        <p><strong>المناخ:</strong> {{ destination.climate }}</p>
                    </div>
                    {% endif %}
                    {% if destination.best_time_to_visit %}
                    <div class="col-md-4">
                        <p><strong>أفضل وقت للزيارة:</strong> {{ destination.best_time_to_visit }}</p>
                    </div>
                    {% endif %}
                </div>
                {% if destination.activities %}
                <div class="mt-3">
                    <p><strong>الأنشطة المتاحة:</strong></p>
                    <p>{{ destination.activities|linebreaks }}</p>
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>

        <div class="col-lg-4">
            <!-- Statistics -->
            <div class="info-card">
                <h5><i class="fas fa-chart-bar"></i>الإحصائيات</h5>
                <div class="stat-item">
                    <span class="stat-value">{{ destination.popularity_score }}</span>
                    <div class="stat-label">نقاط الشعبية</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value">
                        {% if destination.is_active %}
                            <i class="fas fa-check-circle text-success"></i>
                        {% else %}
                            <i class="fas fa-times-circle text-danger"></i>
                        {% endif %}
                    </span>
                    <div class="stat-label">الحالة</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value">
                        {% if destination.is_featured %}
                            <i class="fas fa-star text-warning"></i>
                        {% else %}
                            <i class="fas fa-star-o text-muted"></i>
                        {% endif %}
                    </span>
                    <div class="stat-label">مميز</div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="{% url 'tours:destination_edit' destination.pk %}" class="btn btn-primary btn-action">
                    <i class="fas fa-edit"></i> تعديل الوجهة
                </a>
                <a href="{% url 'tours:destination_delete' destination.pk %}" class="btn btn-danger btn-action"
                   onclick="return confirm('هل أنت متأكد من رغبتك في حذف الوجهة {{ destination.name_ar }}؟');">
                    <i class="fas fa-trash"></i> حذف الوجهة
                </a>
                <a href="{% url 'tours:destination_list' %}" class="btn btn-secondary btn-action">
                    <i class="fas fa-list"></i> قائمة الوجهات
                </a>
                <a href="{% url 'tours:package_list' %}?destination={{ destination.pk }}" class="btn btn-info btn-action">
                    <i class="fas fa-suitcase"></i> الباقات المرتبطة
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Language tab switching
    const languageTabs = document.querySelectorAll('.language-tab');
    const languageContents = document.querySelectorAll('.language-content');
    
    languageTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetLang = this.getAttribute('data-lang');
            
            // Remove active class from all tabs and contents
            languageTabs.forEach(t => t.classList.remove('active'));
            languageContents.forEach(c => c.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            document.getElementById(`content-${targetLang}`).classList.add('active');
        });
    });
    
    console.log('Destination detail loaded');
});
</script>
{% endblock %}
