"""
Suppliers views for the Moroccan Travel Agency ERP system.
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Q, Avg
from django.utils import timezone
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.http import HttpResponse
from django.template.loader import render_to_string
from datetime import datetime, timedelta
from .models import Supplier, SupplierContract, SupplierEvaluation
from .forms import SupplierContractForm, SupplierForm, SupplierEvaluationForm


@login_required
def suppliers_dashboard(request):
    """Suppliers dashboard view."""
    today = timezone.now().date()

    # Supplier statistics
    stats = {
        'total_suppliers': Supplier.objects.count(),
        'active_suppliers': Supplier.objects.filter(is_active=True).count(),
        'preferred_suppliers': Supplier.objects.filter(is_preferred=True).count(),
        'blacklisted_suppliers': Supplier.objects.filter(is_blacklisted=True).count(),
        'active_contracts': SupplierContract.objects.filter(
            status='active',
            start_date__lte=today,
            end_date__gte=today
        ).count(),
        'expiring_contracts': SupplierContract.objects.filter(
            status='active',
            end_date__gte=today,
            end_date__lte=today + timedelta(days=30)
        ).count(),
        'average_rating': Supplier.objects.filter(
            rating__isnull=False
        ).aggregate(avg=Avg('rating'))['avg'] or 0,
        'total_evaluations': SupplierEvaluation.objects.count(),
    }

    # Supplier type distribution
    supplier_types = Supplier.objects.filter(is_active=True).values(
        'supplier_type'
    ).annotate(count=Count('id')).order_by('-count')

    # Recent suppliers
    recent_suppliers = Supplier.objects.filter(is_active=True).order_by('-created_at')[:5]

    # Expiring contracts
    expiring_contracts = SupplierContract.objects.filter(
        status='active',
        end_date__gte=today,
        end_date__lte=today + timedelta(days=60)
    ).select_related('supplier').order_by('end_date')[:10]

    # Top rated suppliers
    top_suppliers = Supplier.objects.filter(
        is_active=True,
        rating__isnull=False
    ).order_by('-rating', '-performance_score')[:5]

    # Recent evaluations
    recent_evaluations = SupplierEvaluation.objects.select_related(
        'supplier'
    ).order_by('-evaluation_date')[:5]

    context = {
        'stats': stats,
        'supplier_types': supplier_types,
        'recent_suppliers': recent_suppliers,
        'expiring_contracts': expiring_contracts,
        'top_suppliers': top_suppliers,
        'recent_evaluations': recent_evaluations,
    }

    return render(request, 'suppliers/dashboard.html', context)


class SupplierListView(LoginRequiredMixin, ListView):
    """List view for suppliers."""
    model = Supplier
    template_name = 'suppliers/supplier_list.html'
    context_object_name = 'suppliers'
    paginate_by = 20

    def get_queryset(self):
        queryset = Supplier.objects.select_related('city').order_by('name_ar')

        # Filter by supplier type
        supplier_type = self.request.GET.get('supplier_type')
        if supplier_type:
            queryset = queryset.filter(supplier_type=supplier_type)

        # Filter by status
        status = self.request.GET.get('status')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
        elif status == 'preferred':
            queryset = queryset.filter(is_preferred=True)
        elif status == 'blacklisted':
            queryset = queryset.filter(is_blacklisted=True)

        # Filter by rating
        rating = self.request.GET.get('rating')
        if rating:
            queryset = queryset.filter(rating=rating)

        # Search
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(supplier_code__icontains=search) |
                Q(name_ar__icontains=search) |
                Q(name_fr__icontains=search) |
                Q(name_en__icontains=search) |
                Q(email__icontains=search) |
                Q(contact_person__icontains=search)
            )

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['supplier_type_choices'] = Supplier.SUPPLIER_TYPE_CHOICES
        context['rating_choices'] = Supplier.RATING_CHOICES
        return context


class SupplierDetailView(LoginRequiredMixin, DetailView):
    """Detail view for supplier."""
    model = Supplier
    template_name = 'suppliers/supplier_detail.html'
    context_object_name = 'supplier'


class SupplierCreateView(LoginRequiredMixin, CreateView):
    """Create view for suppliers."""
    model = Supplier
    form_class = SupplierForm
    template_name = 'suppliers/supplier_form.html'
    success_url = reverse_lazy('suppliers:supplier_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(
            self.request,
            f'تم إنشاء المورد {form.instance.name_ar} بنجاح'
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'إضافة مورد جديد'
        context['submit_text'] = 'إنشاء المورد'
        return context


class SupplierUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for suppliers."""
    model = Supplier
    form_class = SupplierForm
    template_name = 'suppliers/supplier_form.html'
    success_url = reverse_lazy('suppliers:supplier_list')

    def form_valid(self, form):
        messages.success(
            self.request,
            f'تم تحديث المورد {form.instance.name_ar} بنجاح'
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = f'تعديل المورد: {self.object.name_ar}'
        context['submit_text'] = 'حفظ التغييرات'
        return context

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['contracts'] = self.object.contracts.all().order_by('-start_date')
        context['evaluations'] = self.object.evaluations.all().order_by('-evaluation_date')

        # Calculate contract statistics
        today = timezone.now().date()
        context['active_contracts'] = self.object.contracts.filter(
            status='active',
            start_date__lte=today,
            end_date__gte=today
        ).count()

        context['expired_contracts'] = self.object.contracts.filter(
            end_date__lt=today
        ).count()

        # Latest evaluation
        latest_evaluation = self.object.evaluations.order_by('-evaluation_date').first()
        context['latest_evaluation'] = latest_evaluation

        return context


class SupplierContractListView(LoginRequiredMixin, ListView):
    """List view for supplier contracts."""
    model = SupplierContract
    template_name = 'suppliers/contract_list.html'
    context_object_name = 'contracts'
    paginate_by = 20

    def get_queryset(self):
        queryset = SupplierContract.objects.select_related('supplier').order_by('-start_date')

        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by contract type
        contract_type = self.request.GET.get('contract_type')
        if contract_type:
            queryset = queryset.filter(contract_type=contract_type)

        # Filter by supplier
        supplier = self.request.GET.get('supplier')
        if supplier:
            queryset = queryset.filter(supplier_id=supplier)

        # Filter expiring contracts
        expiring = self.request.GET.get('expiring')
        if expiring:
            today = timezone.now().date()
            queryset = queryset.filter(
                status='active',
                end_date__gte=today,
                end_date__lte=today + timedelta(days=30)
            )

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = SupplierContract.STATUS_CHOICES
        context['contract_type_choices'] = SupplierContract.CONTRACT_TYPE_CHOICES
        context['suppliers'] = Supplier.objects.filter(is_active=True)
        return context


class SupplierEvaluationListView(LoginRequiredMixin, ListView):
    """List view for supplier evaluations."""
    model = SupplierEvaluation
    template_name = 'suppliers/evaluation_list.html'
    context_object_name = 'evaluations'
    paginate_by = 20

    def get_queryset(self):
        queryset = SupplierEvaluation.objects.select_related(
            'supplier', 'evaluated_by'
        ).order_by('-evaluation_date')

        # Filter by supplier
        supplier = self.request.GET.get('supplier')
        if supplier:
            queryset = queryset.filter(supplier_id=supplier)

        # Filter by score range
        score_min = self.request.GET.get('score_min')
        score_max = self.request.GET.get('score_max')
        if score_min:
            queryset = queryset.filter(overall_score__gte=score_min)
        if score_max:
            queryset = queryset.filter(overall_score__lte=score_max)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['suppliers'] = Supplier.objects.filter(is_active=True)
        return context


class SupplierContractCreateView(LoginRequiredMixin, CreateView):
    """Create view for supplier contracts."""
    model = SupplierContract
    form_class = SupplierContractForm
    template_name = 'suppliers/contract_form.html'
    success_url = reverse_lazy('suppliers:contract_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(
            self.request,
            f'تم إنشاء العقد {form.instance.title} بنجاح'
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'إضافة عقد جديد'
        context['submit_text'] = 'إنشاء العقد'
        return context


class SupplierContractUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for supplier contracts."""
    model = SupplierContract
    form_class = SupplierContractForm
    template_name = 'suppliers/contract_form.html'
    success_url = reverse_lazy('suppliers:contract_list')

    def form_valid(self, form):
        messages.success(
            self.request,
            f'تم تحديث العقد {form.instance.title} بنجاح'
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = f'تعديل العقد: {self.object.title}'
        context['submit_text'] = 'حفظ التغييرات'
        return context


@login_required
def supplier_reports(request):
    """Supplier reports view."""
    today = timezone.now().date()

    # Supplier statistics
    supplier_stats = {
        'total_suppliers': Supplier.objects.count(),
        'active_suppliers': Supplier.objects.filter(is_active=True).count(),
        'preferred_suppliers': Supplier.objects.filter(is_preferred=True).count(),
        'blacklisted_suppliers': Supplier.objects.filter(is_blacklisted=True).count(),
        'suppliers_by_type': Supplier.objects.values('supplier_type').annotate(
            count=Count('id')
        ).order_by('-count'),
        'suppliers_by_rating': Supplier.objects.filter(
            rating__isnull=False
        ).values('rating').annotate(
            count=Count('id')
        ).order_by('rating'),
    }

    # Contract statistics
    contract_stats = {
        'total_contracts': SupplierContract.objects.count(),
        'active_contracts': SupplierContract.objects.filter(
            status='active',
            start_date__lte=today,
            end_date__gte=today
        ).count(),
        'expired_contracts': SupplierContract.objects.filter(
            end_date__lt=today
        ).count(),
        'expiring_contracts': SupplierContract.objects.filter(
            status='active',
            end_date__gte=today,
            end_date__lte=today + timedelta(days=30)
        ).count(),
        'contracts_by_type': SupplierContract.objects.values('contract_type').annotate(
            count=Count('id')
        ).order_by('-count'),
    }

    # Performance statistics
    performance_stats = {
        'total_evaluations': SupplierEvaluation.objects.count(),
        'average_overall_score': SupplierEvaluation.objects.aggregate(
            avg=Avg('overall_score')
        )['avg'] or 0,
        'average_quality_score': SupplierEvaluation.objects.aggregate(
            avg=Avg('quality_score')
        )['avg'] or 0,
        'average_service_score': SupplierEvaluation.objects.aggregate(
            avg=Avg('service_score')
        )['avg'] or 0,
        'top_performers': Supplier.objects.filter(
            rating__isnull=False
        ).order_by('-rating', '-performance_score')[:10],
    }

    # Recent activity
    recent_activity = {
        'new_suppliers': Supplier.objects.filter(
            created_at__gte=today - timedelta(days=30)
        ).count(),
        'new_contracts': SupplierContract.objects.filter(
            created_at__gte=today - timedelta(days=30)
        ).count(),
        'recent_evaluations': SupplierEvaluation.objects.filter(
            evaluation_date__gte=today - timedelta(days=30)
        ).count(),
    }

    context = {
        'supplier_stats': supplier_stats,
        'contract_stats': contract_stats,
        'performance_stats': performance_stats,
        'recent_activity': recent_activity,
    }

    return render(request, 'suppliers/reports.html', context)


@login_required
def supplier_reports_pdf(request):
    """Generate PDF export of supplier reports."""
    today = timezone.now().date()

    # Get the same data as the regular reports view
    total_suppliers = Supplier.objects.count()

    # Calculate suppliers by type with percentages
    suppliers_by_type = Supplier.objects.values('supplier_type').annotate(
        count=Count('id')
    ).order_by('-count')

    # Add percentage calculation
    for type_data in suppliers_by_type:
        if total_suppliers > 0:
            type_data['percentage'] = (type_data['count'] * 100.0) / total_suppliers
        else:
            type_data['percentage'] = 0

    supplier_stats = {
        'total_suppliers': total_suppliers,
        'active_suppliers': Supplier.objects.filter(is_active=True).count(),
        'preferred_suppliers': Supplier.objects.filter(is_preferred=True).count(),
        'blacklisted_suppliers': Supplier.objects.filter(is_blacklisted=True).count(),
        'suppliers_by_type': suppliers_by_type,
        'suppliers_by_rating': Supplier.objects.filter(
            rating__isnull=False
        ).values('rating').annotate(
            count=Count('id')
        ).order_by('rating'),
    }

    # Contract statistics
    contract_stats = {
        'total_contracts': SupplierContract.objects.count(),
        'active_contracts': SupplierContract.objects.filter(status='active').count(),
        'expired_contracts': SupplierContract.objects.filter(
            status='expired'
        ).count(),
        'expiring_soon': SupplierContract.objects.filter(
            status='active',
            end_date__gte=today,
            end_date__lte=today + timedelta(days=30)
        ).count(),
        'contracts_by_type': SupplierContract.objects.values('contract_type').annotate(
            count=Count('id')
        ).order_by('-count'),
        'total_contract_value': SupplierContract.objects.filter(
            status='active'
        ).aggregate(total=Sum('contract_value'))['total'] or 0,
    }

    # Performance statistics
    performance_stats = {
        'total_evaluations': SupplierEvaluation.objects.count(),
        'average_quality_score': SupplierEvaluation.objects.aggregate(
            avg=Avg('quality_score')
        )['avg'] or 0,
        'average_delivery_score': SupplierEvaluation.objects.aggregate(
            avg=Avg('delivery_score')
        )['avg'] or 0,
        'average_service_score': SupplierEvaluation.objects.aggregate(
            avg=Avg('service_score')
        )['avg'] or 0,
        'average_price_score': SupplierEvaluation.objects.aggregate(
            avg=Avg('price_score')
        )['avg'] or 0,
        'average_overall_score': SupplierEvaluation.objects.aggregate(
            avg=Avg('overall_score')
        )['avg'] or 0,
    }

    # Recent activity
    recent_activity = {
        'new_suppliers': Supplier.objects.filter(
            created_at__gte=today - timedelta(days=30)
        ).count(),
        'new_contracts': SupplierContract.objects.filter(
            created_at__gte=today - timedelta(days=30)
        ).count(),
        'recent_evaluations': SupplierEvaluation.objects.filter(
            evaluation_date__gte=today - timedelta(days=30)
        ).count(),
    }

    # Get detailed supplier list for the report
    suppliers_list = Supplier.objects.filter(is_active=True).select_related().order_by('name_ar')[:50]

    # Get recent contracts
    recent_contracts = SupplierContract.objects.filter(
        status='active'
    ).select_related('supplier').order_by('-created_at')[:20]

    context = {
        'supplier_stats': supplier_stats,
        'contract_stats': contract_stats,
        'performance_stats': performance_stats,
        'recent_activity': recent_activity,
        'suppliers_list': suppliers_list,
        'recent_contracts': recent_contracts,
        'report_date': today,
        'company_name': 'وكالة السفر المغربية',
        'report_title': 'تقرير الموردين الشامل',
    }

    # Render the PDF template
    html_content = render_to_string('suppliers/reports_pdf.html', context)

    # Create HTTP response with PDF content type
    response = HttpResponse(html_content, content_type='text/html')
    response['Content-Disposition'] = f'attachment; filename="suppliers_report_{today}.html"'

    return response
