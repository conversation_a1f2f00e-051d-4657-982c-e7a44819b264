{% extends 'base.html' %}
{% load static %}

{% block title %}الوجهات السياحية{% endblock %}

{% block extra_css %}
<style>
    .destination-card {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        height: 100%;
    }
    
    .destination-card:hover {
        transform: translateY(-5px);
    }
    
    .destination-image {
        height: 200px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
    }
    
    .destination-content {
        padding: 20px;
    }
    
    .destination-title {
        font-size: 1.25rem;
        font-weight: bold;
        margin-bottom: 10px;
        color: #333;
    }
    
    .destination-country {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 15px;
    }
    
    .destination-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #e9ecef;
    }
    
    .stats-row {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-globe text-primary me-2"></i>
                        الوجهات السياحية
                    </h1>
                    <p class="text-muted mb-0">إدارة وعرض جميع الوجهات السياحية</p>
                </div>
                <div>
                    <a href="{% url 'tours:destination_add' %}" class="btn btn-primary" style="z-index: 1000; position: relative;">
                        <i class="fas fa-plus me-2"></i>
                        إضافة وجهة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-row">
        <div class="row text-center">
            <div class="col-md-3">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="me-3">
                        <i class="fas fa-map-marker-alt fa-2x text-primary"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">{{ destinations.count }}</h4>
                        <small class="text-muted">إجمالي الوجهات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="me-3">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">{{ destinations|length }}</h4>
                        <small class="text-muted">الوجهات النشطة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="me-3">
                        <i class="fas fa-star fa-2x text-warning"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">0</h4>
                        <small class="text-muted">الوجهات المميزة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="me-3">
                        <i class="fas fa-flag fa-2x text-info"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">0</h4>
                        <small class="text-muted">الدول</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Destinations Grid -->
    <div class="row">
        {% for destination in destinations %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="destination-card">
                <div class="destination-image">
                    {% if destination.main_image %}
                        <img src="{{ destination.main_image.url }}" style="width: 100%; height: 100%; object-fit: cover;" alt="{{ destination.name_ar }}">
                    {% else %}
                        <i class="fas fa-map-marker-alt"></i>
                    {% endif %}
                </div>
                
                <div class="destination-content">
                    <div class="destination-title">{{ destination.name_ar }}</div>
                    <div class="destination-country">
                        <i class="fas fa-flag me-1"></i>
                        {{ destination.country.name_ar }}
                    </div>
                    
                    {% if destination.description_ar %}
                    <p class="text-muted small">
                        {{ destination.description_ar|truncatewords:20 }}
                    </p>
                    {% endif %}
                    
                    <div class="destination-stats">
                        <div>
                            <small class="text-muted">الباقات</small><br>
                            <strong>0</strong>
                        </div>
                        <div>
                            <small class="text-muted">الشعبية</small><br>
                            <strong>{{ destination.popularity_score }}</strong>
                        </div>
                        <div class="btn-group" role="group">
                            <a href="{% url 'tours:destination_detail' destination.pk %}" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'tours:destination_edit' destination.pk %}" 
                               class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-edit"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-globe fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد وجهات سياحية</h4>
                <p class="text-muted">ابدأ بإضافة وجهة سياحية جديدة</p>
                <a href="{% url 'tours:destination_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة وجهة جديدة
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="row">
        <div class="col-12">
            <nav aria-label="صفحات الوجهات">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    console.log('Destinations list loaded');
});
</script>
{% endblock %}
