{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}تقارير الموردين{% endblock %}

{% block extra_css %}
<style>
.report-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.report-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.report-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    text-align: center;
}

.stat-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.2);
}

.stat-card.suppliers {
    border-left-color: #38ef7d;
}

.stat-card.contracts {
    border-left-color: #fdbb2d;
}

.stat-card.evaluations {
    border-left-color: #667eea;
}

.stat-card.performance {
    border-left-color: #ff6b6b;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 10px;
    display: block;
}

.stat-card.suppliers .stat-number {
    color: #38ef7d;
}

.stat-card.contracts .stat-number {
    color: #fdbb2d;
}

.stat-card.evaluations .stat-number {
    color: #667eea;
}

.stat-card.performance .stat-number {
    color: #ff6b6b;
}

.stat-label {
    color: #6c757d;
    font-size: 1rem;
    font-weight: 500;
}

.chart-container {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.chart-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.chart-title i {
    margin-left: 10px;
    color: #667eea;
}

.supplier-type-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.supplier-type-name {
    font-weight: 500;
    color: #495057;
}

.supplier-type-count {
    background: #667eea;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: 500;
}

.rating-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #fdbb2d;
}

.rating-stars {
    color: #fdbb2d;
}

.rating-count {
    background: #fdbb2d;
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

.progress-bar-custom {
    height: 8px;
    border-radius: 10px;
    background: #e9ecef;
    overflow: hidden;
    margin-top: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.btn-export {
    background: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
    margin-right: 10px;
}

.btn-export:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(56, 239, 125, 0.4);
    color: white;
}

.btn-print {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-print:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.metric-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.metric-row:last-child {
    border-bottom: none;
}

.metric-label {
    color: #6c757d;
    font-weight: 500;
}

.metric-value {
    color: #495057;
    font-weight: 600;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-left: 15px;
    color: #667eea;
}

.top-performer-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background: linear-gradient(135deg, rgba(56,239,125,0.1) 0%, rgba(56,239,125,0.05) 100%);
    border-radius: 10px;
    border-left: 4px solid #38ef7d;
}

.performer-info {
    flex-grow: 1;
}

.performer-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.performer-type {
    color: #6c757d;
    font-size: 0.9rem;
}

.performer-score {
    background: #38ef7d;
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 600;
}

@media print {
    .btn-export, .btn-print {
        display: none;
    }
    
    .report-card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="report-header">
        <h1 class="h2 mb-3">📊 تقارير الموردين</h1>
        <p class="mb-4">تقرير شامل عن أداء وإحصائيات الموردين</p>
        <div>
            <button class="btn btn-export">
                <i class="fas fa-download"></i> تصدير PDF
            </button>
            <button class="btn btn-print" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة
            </button>
            <a href="{% url 'suppliers:dashboard' %}" class="btn btn-outline-light">
                <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>

    <!-- Key Statistics -->
    <div class="stat-grid">
        <div class="stat-card suppliers">
            <span class="stat-number">{{ supplier_stats.total_suppliers }}</span>
            <div class="stat-label">إجمالي الموردين</div>
        </div>
        <div class="stat-card contracts">
            <span class="stat-number">{{ contract_stats.active_contracts }}</span>
            <div class="stat-label">عقود نشطة</div>
        </div>
        <div class="stat-card evaluations">
            <span class="stat-number">{{ performance_stats.total_evaluations }}</span>
            <div class="stat-label">إجمالي التقييمات</div>
        </div>
        <div class="stat-card performance">
            <span class="stat-number">{{ performance_stats.average_overall_score|floatformat:1 }}</span>
            <div class="stat-label">متوسط الأداء</div>
        </div>
    </div>

    <div class="row">
        <!-- Supplier Statistics -->
        <div class="col-lg-6">
            <div class="report-card">
                <h3 class="section-title">
                    <i class="fas fa-building"></i>
                    إحصائيات الموردين
                </h3>
                
                <div class="metric-row">
                    <span class="metric-label">إجمالي الموردين</span>
                    <span class="metric-value">{{ supplier_stats.total_suppliers }}</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">موردين نشطين</span>
                    <span class="metric-value">{{ supplier_stats.active_suppliers }}</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">موردين مفضلين</span>
                    <span class="metric-value">{{ supplier_stats.preferred_suppliers }}</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">قائمة سوداء</span>
                    <span class="metric-value">{{ supplier_stats.blacklisted_suppliers }}</span>
                </div>
                
                <h5 class="mt-4 mb-3">توزيع الموردين حسب النوع</h5>
                {% for type_data in supplier_stats.suppliers_by_type %}
                <div class="supplier-type-item">
                    <span class="supplier-type-name">{{ type_data.supplier_type }}</span>
                    <span class="supplier-type-count">{{ type_data.count }} مورد</span>
                </div>
                {% empty %}
                <p class="text-muted text-center">لا توجد بيانات</p>
                {% endfor %}
            </div>
        </div>

        <!-- Contract Statistics -->
        <div class="col-lg-6">
            <div class="report-card">
                <h3 class="section-title">
                    <i class="fas fa-file-contract"></i>
                    إحصائيات العقود
                </h3>
                
                <div class="metric-row">
                    <span class="metric-label">إجمالي العقود</span>
                    <span class="metric-value">{{ contract_stats.total_contracts }}</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">عقود نشطة</span>
                    <span class="metric-value">{{ contract_stats.active_contracts }}</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">عقود منتهية</span>
                    <span class="metric-value">{{ contract_stats.expired_contracts }}</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">عقود تنتهي قريباً</span>
                    <span class="metric-value">{{ contract_stats.expiring_contracts }}</span>
                </div>
                
                <h5 class="mt-4 mb-3">العقود حسب النوع</h5>
                {% for contract_type in contract_stats.contracts_by_type %}
                <div class="supplier-type-item">
                    <span class="supplier-type-name">{{ contract_type.contract_type }}</span>
                    <span class="supplier-type-count">{{ contract_type.count }}</span>
                </div>
                {% empty %}
                <p class="text-muted text-center">لا توجد عقود</p>
                {% endfor %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Performance Statistics -->
        <div class="col-lg-6">
            <div class="report-card">
                <h3 class="section-title">
                    <i class="fas fa-chart-line"></i>
                    إحصائيات الأداء
                </h3>
                
                <div class="metric-row">
                    <span class="metric-label">إجمالي التقييمات</span>
                    <span class="metric-value">{{ performance_stats.total_evaluations }}</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">متوسط النقاط الإجمالية</span>
                    <span class="metric-value">{{ performance_stats.average_overall_score|floatformat:1 }}/5</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">متوسط نقاط الجودة</span>
                    <span class="metric-value">{{ performance_stats.average_quality_score|floatformat:1 }}/5</span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">متوسط نقاط الخدمة</span>
                    <span class="metric-value">{{ performance_stats.average_service_score|floatformat:1 }}/5</span>
                </div>
                
                <h5 class="mt-4 mb-3">التقييمات حسب النجوم</h5>
                {% for rating_data in supplier_stats.suppliers_by_rating %}
                <div class="rating-item">
                    <div class="rating-stars">
                        {% for i in "12345"|make_list %}
                            {% if forloop.counter <= rating_data.rating %}
                                <i class="fas fa-star"></i>
                            {% else %}
                                <i class="far fa-star"></i>
                            {% endif %}
                        {% endfor %}
                    </div>
                    <span class="rating-count">{{ rating_data.count }}</span>
                </div>
                {% empty %}
                <p class="text-muted text-center">لا توجد تقييمات</p>
                {% endfor %}
            </div>
        </div>

        <!-- Top Performers -->
        <div class="col-lg-6">
            <div class="report-card">
                <h3 class="section-title">
                    <i class="fas fa-trophy"></i>
                    أفضل الموردين
                </h3>
                
                {% for supplier in performance_stats.top_performers %}
                <div class="top-performer-item">
                    <div class="performer-info">
                        <div class="performer-name">{{ supplier.name_ar }}</div>
                        <div class="performer-type">{{ supplier.get_supplier_type_display }}</div>
                    </div>
                    <div class="performer-score">
                        {% if supplier.rating %}{{ supplier.rating }}/5{% else %}غير مقيم{% endif %}
                    </div>
                </div>
                {% empty %}
                <p class="text-muted text-center">لا توجد بيانات أداء</p>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="report-card">
        <h3 class="section-title">
            <i class="fas fa-clock"></i>
            النشاط الأخير (آخر 30 يوم)
        </h3>
        
        <div class="row text-center">
            <div class="col-md-4">
                <div class="metric-row">
                    <span class="metric-label">موردين جدد</span>
                    <span class="metric-value">{{ recent_activity.new_suppliers }}</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="metric-row">
                    <span class="metric-label">عقود جديدة</span>
                    <span class="metric-value">{{ recent_activity.new_contracts }}</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="metric-row">
                    <span class="metric-label">تقييمات جديدة</span>
                    <span class="metric-value">{{ recent_activity.recent_evaluations }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Footer -->
    <div class="report-card">
        <div class="row text-center">
            <div class="col-md-3">
                <h4 class="text-primary">{{ supplier_stats.active_suppliers }}</h4>
                <p class="text-muted">مورد نشط</p>
            </div>
            <div class="col-md-3">
                <h4 class="text-warning">{{ contract_stats.expiring_contracts }}</h4>
                <p class="text-muted">عقد ينتهي قريباً</p>
            </div>
            <div class="col-md-3">
                <h4 class="text-info">{{ performance_stats.average_overall_score|floatformat:1 }}</h4>
                <p class="text-muted">متوسط الأداء</p>
            </div>
            <div class="col-md-3">
                <h4 class="text-success">{{ supplier_stats.preferred_suppliers }}</h4>
                <p class="text-muted">مورد مفضل</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Print functionality
function printReport() {
    window.print();
}

// Export functionality (placeholder)
function exportToPDF() {
    alert('سيتم تنفيذ وظيفة التصدير قريباً');
}
</script>
{% endblock %}
