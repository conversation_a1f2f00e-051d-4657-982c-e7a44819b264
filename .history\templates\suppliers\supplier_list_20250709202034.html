{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}قائمة الموردين{% endblock %}

{% block extra_css %}
<style>
.supplier-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
}

.supplier-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.supplier-card.preferred {
    border-left-color: #38ef7d;
    background: linear-gradient(135deg, rgba(56,239,125,0.05) 0%, rgba(56,239,125,0.02) 100%);
}

.supplier-card.blacklisted {
    border-left-color: #ff6b6b;
    background: linear-gradient(135deg, rgba(255,107,107,0.05) 0%, rgba(255,107,107,0.02) 100%);
}

.supplier-card.inactive {
    border-left-color: #6c757d;
    opacity: 0.7;
}

.supplier-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-right: 15px;
}

.supplier-info h5 {
    margin-bottom: 5px;
    color: #495057;
}

.supplier-info p {
    margin-bottom: 3px;
    color: #6c757d;
    font-size: 0.9rem;
}

.rating-stars {
    color: #fdbb2d;
    margin-top: 5px;
}

.rating-stars .far {
    color: #dee2e6;
}

.status-badges {
    display: flex;
    gap: 5px;
    margin-top: 10px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-active {
    background: #d1edff;
    color: #0c63e4;
}

.badge-inactive {
    background: #e2e3e5;
    color: #383d41;
}

.badge-preferred {
    background: #d4edda;
    color: #155724;
}

.badge-blacklisted {
    background: #f8d7da;
    color: #721c24;
}

.filter-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
}

.filter-card .form-control,
.filter-card .form-select {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    border-radius: 10px;
}

.filter-card .form-control::placeholder {
    color: rgba(255,255,255,0.7);
}

.filter-card .form-control:focus,
.filter-card .form-select:focus {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
}

.btn-filter {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    border-radius: 10px;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.btn-filter:hover {
    background: rgba(255,255,255,0.3);
    color: white;
    transform: translateY(-2px);
}

.btn-add {
    background: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-add:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(56, 239, 125, 0.4);
    color: white;
}

.supplier-actions {
    display: flex;
    gap: 10px;
}

.btn-action {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.btn-view {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

.btn-view:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-edit {
    background: linear-gradient(135deg, #fdbb2d 0%, #f39c12 100%);
    border: none;
    color: white;
}

.btn-edit:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(253, 187, 45, 0.4);
    color: white;
}

.stats-row {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
}

.stat-item {
    text-align: center;
    padding: 15px;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #dee2e6;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">🏢 قائمة الموردين</h1>
                    <p class="text-muted">إدارة وعرض جميع موردي الخدمات</p>
                </div>
                <div>
                    <a href="{% url 'suppliers:dashboard' %}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
                    </a>
                    <a href="{% url 'suppliers:supplier_add' %}" class="btn btn-add">
                        <i class="fas fa-plus"></i> إضافة مورد جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-row">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ suppliers|length }}</div>
                    <div class="stat-label">إجمالي الموردين</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">0</div>
                    <div class="stat-label">موردين نشطين</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">0</div>
                    <div class="stat-label">موردين مفضلين</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ page_obj.paginator.count }}</div>
                    <div class="stat-label">النتائج المعروضة</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="get" class="row align-items-end">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" placeholder="البحث بالاسم، الكود، أو البريد..." value="{{ request.GET.search }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">النوع</label>
                <select name="supplier_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    {% for type_key, type_name in supplier_type_choices %}
                    <option value="{{ type_key }}" {% if request.GET.supplier_type == type_key %}selected{% endif %}>
                        {{ type_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>نشط</option>
                    <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>غير نشط</option>
                    <option value="preferred" {% if request.GET.status == 'preferred' %}selected{% endif %}>مفضل</option>
                    <option value="blacklisted" {% if request.GET.status == 'blacklisted' %}selected{% endif %}>قائمة سوداء</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">التقييم</label>
                <select name="rating" class="form-select">
                    <option value="">جميع التقييمات</option>
                    {% for rating_key, rating_name in rating_choices %}
                    <option value="{{ rating_key }}" {% if request.GET.rating == rating_key|stringformat:"s" %}selected{% endif %}>
                        {{ rating_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-filter w-100">
                    <i class="fas fa-search"></i> بحث
                </button>
            </div>
        </form>
    </div>

    <!-- Supplier List -->
    {% if suppliers %}
    <div class="row">
        {% for supplier in suppliers %}
        <div class="col-lg-6 col-xl-4">
            <div class="supplier-card {% if supplier.is_preferred %}preferred{% elif supplier.is_blacklisted %}blacklisted{% elif not supplier.is_active %}inactive{% endif %}">
                <div class="d-flex align-items-center mb-3">
                    <div class="supplier-avatar">
                        {{ supplier.name_ar|first }}
                    </div>
                    <div class="supplier-info flex-grow-1">
                        <h5>{{ supplier.name_ar }}</h5>
                        <p><i class="fas fa-tag me-1"></i>{{ supplier.get_supplier_type_display }}</p>
                        <p><i class="fas fa-code me-1"></i>{{ supplier.supplier_code }}</p>
                        <p><i class="fas fa-envelope me-1"></i>{{ supplier.email|default:"غير محدد" }}</p>
                        {% if supplier.rating %}
                        <div class="rating-stars">
                            {% for i in "12345"|make_list %}
                                {% if forloop.counter <= supplier.rating %}
                                    <i class="fas fa-star"></i>
                                {% else %}
                                    <i class="far fa-star"></i>
                                {% endif %}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="status-badges">
                    <span class="status-badge badge-{{ supplier.is_active|yesno:'active,inactive' }}">
                        {{ supplier.is_active|yesno:'نشط,غير نشط' }}
                    </span>
                    {% if supplier.is_preferred %}
                    <span class="status-badge badge-preferred">مفضل</span>
                    {% endif %}
                    {% if supplier.is_blacklisted %}
                    <span class="status-badge badge-blacklisted">قائمة سوداء</span>
                    {% endif %}
                </div>
                
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        <small><i class="fas fa-user me-1"></i>{{ supplier.contact_person|default:"غير محدد" }}</small>
                    </div>
                    
                    <div class="supplier-actions">
                        <a href="{% url 'suppliers:supplier_detail' supplier.pk %}" class="btn btn-view btn-action" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{% url 'suppliers:supplier_edit' supplier.pk %}" class="btn btn-edit btn-action" title="تعديل المورد">
                            <i class="fas fa-edit"></i>
                        </a>
                    </div>
                </div>
                
                {% if supplier.performance_score %}
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-chart-line me-1"></i>نقاط الأداء: {{ supplier.performance_score }}
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Supplier pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.supplier_type %}supplier_type={{ request.GET.supplier_type }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.rating %}rating={{ request.GET.rating }}&{% endif %}page={{ page_obj.previous_page_number }}">السابق</a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.supplier_type %}supplier_type={{ request.GET.supplier_type }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.rating %}rating={{ request.GET.rating }}&{% endif %}page={{ num }}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.supplier_type %}supplier_type={{ request.GET.supplier_type }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.rating %}rating={{ request.GET.rating }}&{% endif %}page={{ page_obj.next_page_number }}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}

    {% else %}
    <!-- Empty State -->
    <div class="empty-state">
        <i class="fas fa-building"></i>
        <h4>لا توجد موردين</h4>
        <p>لم يتم العثور على أي موردين بناءً على معايير البحث المحددة.</p>
        <button class="btn btn-add mt-3">
            <i class="fas fa-plus"></i> إضافة مورد جديد
        </button>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit form on filter change
document.querySelectorAll('select[name="supplier_type"], select[name="status"], select[name="rating"]').forEach(function(select) {
    select.addEventListener('change', function() {
        this.form.submit();
    });
});

// Clear filters
function clearFilters() {
    window.location.href = '{% url "suppliers:supplier_list" %}';
}
</script>
{% endblock %}
